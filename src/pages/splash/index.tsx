import { useEffect } from 'react'
import { IonContent, IonPage } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import useAuth from '../../tina/stores/authStore'

/**
 * 移动端启动页面组件
 * 直接跳转到相应页面，不再显示启动画面
 */
export default function SplashPage() {
  const history = useHistory()
  const auth = useAuth()

  useEffect(() => {
    // 直接判断登录状态并跳转到相应页面
    if (auth.isLoggedIn()) {
      history.replace('/conversation/1')
    } else {
      history.replace('/home')
    }
  }, [history, auth])

  return (
    <IonPage>
      <IonContent>
        {/* 简单的加载指示器 */}
        <div className="h-full w-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </IonContent>
    </IonPage>
  )
}
