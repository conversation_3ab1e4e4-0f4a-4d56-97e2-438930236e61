import { useEffect, useState, useCallback } from 'react'
import { TaskCenter } from '@/pages/task-center/task-center.tsx'
import { IonPage } from '@ionic/react'
import ConfirmationDialogManager from '@/components/ConfirmationDialogManager'
import ConversationFooter from './ConversationFooter'
import ConversationHeader from './ConversationHeader'
import ConversationList from './ConversationList'
import ScrollToBottomButton from './ConversationList/ScrollToBottomButton'
import { ConversationAPIProvider } from './context'

// 内部组件，用于渲染对话内容
const ConversationContent = () => {
  const [scrollData, setScrollData] = useState<{
    showScrollButton: boolean
    handleScrollToBottomClick: () => void
  }>({
    showScrollButton: false,
    handleScrollToBottomClick: () => {}
  })

  const handleScrollData = useCallback((data: { showScrollButton: boolean, handleScrollToBottomClick: () => void }) => {
    setScrollData(data)
  }, [])

  return (
    <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md relative'>
      <ConversationHeader />
      <ConversationList onScrollData={handleScrollData} />
      
      {/* 滚动到底部按钮 - 悬浮在输入框上方，居中显示 */}
      <div className='absolute bottom-20 left-1/2 transform -translate-x-1/2 z-40'>
        <ScrollToBottomButton
          show={scrollData.showScrollButton}
          onClick={scrollData.handleScrollToBottomClick}
        />
      </div>

      {/* 悬浮输入框 - 绝对定位，不占用文档流空间 */}
      <div className='absolute bottom-0 left-0 right-0 z-50'>
        <ConversationFooter />
      </div>
    </div>
  )
}

const Conversation = () => {
  return (
    <>
      <TaskCenter className='mx-auto sm:max-w-md' />
      <IonPage id='main-content'>
        <ConversationAPIProvider>
          <ConversationContent />
          {/* 独立的确认对话框管理器 */}
          <ConfirmationDialogManager />
        </ConversationAPIProvider>
      </IonPage>
    </>
  )
}

export default Conversation
