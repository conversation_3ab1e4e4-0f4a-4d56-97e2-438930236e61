import type {
  CSSProperties,
  ComponentType,
  MouseE<PERSON>Handler,
  PropsWithChildren,
  ReactNode,
} from 'react'
import type { IConversationItemBase } from '@/stateV2/conversation'
import { getModeValueSnapshot } from '@/stateV2/mode'
import { type IStateProfile, profileAtom } from '@/stateV2/profile'
import { css } from '@emotion/react'
import { useDebounceFn } from 'ahooks'
import { useAtomValue } from 'jotai'
import { twJoin, twMerge } from 'tailwind-merge'
import { h } from '@/components/HashAssets'
import useModeNavigate from '@/components/useModeNavigate'
import { useConversationAPI } from '../../context'
import showAvatarAtom from '@/stateV2/showAvatarAtom'

interface Props<P = AnyObject> {
  upperText: IConversationItemBase['upperText']
  senderId: IStateProfile['id']
  innerBlockClassName?: string
  blockClassName?: string
  blockStyle?: CSSProperties
  extraElement?: ReactNode
  hideAvatar?: boolean
  innerBlockComponent?: ComponentType<P> | string
  innerBlockProps?: P
  onClick?: MouseEventHandler<HTMLDivElement>
}

const CommonBlock = <P extends AnyObject>({
  upperText,
  senderId,
  children,
  innerBlockClassName,
  blockClassName,
  blockStyle,
  extraElement,
  hideAvatar,
  innerBlockComponent: InnerBlockComponent = 'div',
  innerBlockProps,
  onClick,
}: PropsWithChildren<Props<P>>) => {
  const profile = useAtomValue(profileAtom(senderId))!
  const { avatarInfo } = profile
  const navigate = useModeNavigate({ silence: true })
  const { demoService } = useConversationAPI()

  // 读取 showAvatar 开关
  const showAvatar = useAtomValue(showAvatarAtom)
  // 调试信息

  const handleClick: MouseEventHandler<HTMLImageElement> = (ev) => {
    const { detail: count } = ev
    if (count === 2) {
      handleDoubliClick()
    } else if (count === 1) {
      // navigate.push(`/wechat/friend/${senderId}`)
    }
  }

  const handleDoubliClick = () => {
    if (getModeValueSnapshot() === 'edit') return
    demoService.sendTickleText(senderId)
  }

  const { run: debouncedHandleClick } = useDebounceFn(handleClick, {
    wait: 200,
  })

  return (
    <>
      {upperText && (
        <div className='m-auto text-xs text-black/50'>{upperText}</div>
      )}
      <div
        className={twMerge(
          'relative flex max-w-[95%] space-x-3 group-[.mine]:ml-auto group-[.mine]:flex-row-reverse group-[.mine]:space-x-reverse',
          blockClassName,
        )}
        style={blockStyle}
        onClick={onClick}
      >
        {!showAvatar ? <div style={{ width: 0, minWidth: 0, height: 0 }} /> : (
          <h.img
            src={avatarInfo}
            className={twJoin(
              'h-10 w-10 min-w-10 cursor-pointer rounded object-cover object-center',
              hideAvatar && 'invisible',
            )}
            onClick={debouncedHandleClick}
          />
        )  }
        <InnerBlockComponent
          {...({
            className: twMerge(
              'relative p-[12px]',
              // AI消息：左上角直角，其他圆角
              'group-[.friend]:rounded-2xl group-[.friend]:rounded-tl-none',
              // 用户消息：右上角直角，其他圆角  
              'group-[.mine]:rounded-2xl group-[.mine]:rounded-tr-none',
              'group-[.friend]:max-w-[95%] group-[.friend]:break-words',
              'group-[.mine]:max-w-[280px] group-[.mine]:w-fit',
              innerBlockClassName,
            ),
            ...(innerBlockProps as P),
          } as any)}
        >
          {children}
        </InnerBlockComponent>
        {extraElement}
      </div>
    </>
  )
}

export default CommonBlock
