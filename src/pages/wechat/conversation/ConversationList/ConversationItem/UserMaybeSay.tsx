import { memo, useState } from 'react'
import type { IConversationTypeUserMaybeSay } from '@/stateV2/conversation'
import type { IStateProfile } from '@/stateV2/profile'
import { useConversationAPI } from '../../context'

type Props = {
  upperText: IConversationTypeUserMaybeSay['upperText']
  senderId: IStateProfile['id']
  suggestions: IConversationTypeUserMaybeSay['suggestions']
}

const UserMaybeSay = ({ upperText, senderId, suggestions }: Props) => {
  const { sendTextMessage, sendNotification, updateNotification } =
    useConversationAPI()
  const [isVisible, setIsVisible] = useState(true)

  const handleSuggestionClick = (suggestion: string) => {
    // 立即隐藏组件，避免在发送消息过程中重新显示
    setIsVisible(false)

    // 发送用户消息
    sendTextMessage(suggestion)


  }

  if (!isVisible) {
    return null
  }

  return (
    <>
      {upperText && (
        <div className='m-auto mb-2 text-xs text-black/50'>{upperText}</div>
      )}
      <div className='flex flex-col items-end gap-2'>
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            onClick={() => handleSuggestionClick(suggestion)}
            className='inline-flex max-w-xs items-center rounded-full border border-gray-200 bg-white px-3 py-2 text-sm shadow-sm transition-colors duration-200 hover:border-gray-300 hover:bg-gray-50 hover:shadow-md'
          >
            <span className='truncate'>{suggestion}</span>
            <span className='ml-1 flex-shrink-0 text-gray-400'>↑ </span>
          </button>
        ))}
      </div>
    </>
  )
}

export default memo(UserMaybeSay)
