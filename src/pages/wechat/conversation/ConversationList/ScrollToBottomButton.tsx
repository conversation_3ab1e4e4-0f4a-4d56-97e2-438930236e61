import { useCallback } from 'react'
import { IonIcon } from '@ionic/react'
import { chevronDown } from 'ionicons/icons'
import { twJoin } from 'tailwind-merge'

interface ScrollToBottomButtonProps {
  show: boolean
  onClick: () => void
  className?: string
}

const ScrollToBottomButton = ({
  show,
  onClick,
  className,
}: ScrollToBottomButtonProps) => {
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
      onClick()
    },
    [onClick],
  )

  if (!show) {
    return null
  }

  return (
    <div
      className={twJoin(
        'animate-in fade-in slide-in-from-bottom-2 duration-200',
        className,
      )}
    >
      <button
        onClick={handleClick}
        className={twJoin(
          'flex h-10 w-10 items-center justify-center',
          'rounded-full bg-white shadow-lg',
          'border border-gray-200',
          'hover:bg-gray-50 active:bg-gray-100',
          'transition-all duration-150',
          'focus:outline-none',
        )}
        aria-label='滚动到底部'
      >
        <IonIcon icon={chevronDown} className='h-5 w-5 text-gray-600' />
      </button>
    </div>
  )
}

export default ScrollToBottomButton
