import { useEffect, useState } from 'react'
import { IonContent, IonPage, IonHeader, IonToolbar, IonButtons, IonTitle } from '@ionic/react'
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser'
import { Toast } from '@capacitor/toast'
import { Capacitor } from '@capacitor/core'
import { AppUpdate, AppUpdateAvailability } from '@capawesome/capacitor-app-update'
import { HeaderBackButton } from '../../components/ui/back-button'
import { betaInfo } from '../../data/betaInfo'
import { updateConfig } from '../../data/updateConfig'
import { checkForUpdates, shouldUpdate } from '../../utils/updateChecker'

/**
 * 版本更新页面组件
 * 提供应用版本检查和更新功能
 */
export default function VersionUpdatePage() {
    const [version, setVersion] = useState('')
    const [updateAvailable, setUpdateAvailable] = useState(false)
    const [updateType, setUpdateType] = useState<'immediate' | 'flexible' | null>(null)
    const [customApkUrl, setCustomApkUrl] = useState<string | null>(null)
    const [updateNotes, setUpdateNotes] = useState<string>('')
    const [isChecking, setIsChecking] = useState(true)

    useEffect(() => {
        // 获取当前版本号
        let v = updateConfig?.versionName || betaInfo?.versionInfo
        if (!v) {
            try {
                // @ts-ignore
                v = __APP_VERSION__
            } catch { }
        }
        setVersion(v || '')

        // 检查更新
        checkForUpdates()
            .then(data => {
                if (data && shouldUpdate(data.versionCode)) {
                    setUpdateAvailable(true)
                    setCustomApkUrl(data.apkUrl)
                    setUpdateNotes(data.updateNotes)
                } else {
                    setUpdateAvailable(false)
                }
            })
            .catch((error) => {
                console.warn('检查更新失败:', error)
                setUpdateAvailable(false)
            })
            .finally(() => {
                setIsChecking(false)
            })

        // 检查热更新（仅在原生环境下）
        if (Capacitor.isNativePlatform()) {
            AppUpdate.getAppUpdateInfo().then((result) => {
                if (result.updateAvailability === AppUpdateAvailability.UPDATE_AVAILABLE) {
                    setUpdateAvailable(true)
                    if (result.immediateUpdateAllowed) setUpdateType('immediate')
                    else if (result.flexibleUpdateAllowed) setUpdateType('flexible')
                }
            })
        }
    }, [])

    // 立即更新
    const handleImmediateUpdate = async () => {
        try {
            await AppUpdate.performImmediateUpdate()
        } catch (e) {
            alert('更新失败，请前往应用市场手动更新')
            await AppUpdate.openAppStore()
        }
    }

    // 弹性更新
    const handleFlexibleUpdate = async () => {
        try {
            await AppUpdate.startFlexibleUpdate()
            await AppUpdate.completeFlexibleUpdate()
        } catch (e) {
            alert('更新失败，请前往应用市场手动更新')
            await AppUpdate.openAppStore()
        }
    }

    // 立即更新（自定义服务器）
    const handleCustomUpdate = async () => {
        if (customApkUrl) {
            try {
                if (Capacitor.isNativePlatform()) {
                    // 在原生环境中使用 Capacitor Browser 插件
                    await Browser.open({
                        url: customApkUrl,
                        windowName: '_system' // 使用系统浏览器打开
                    })
                    await Toast.show({
                        text: '正在打开浏览器下载...',
                        duration: 'short',
                        position: 'center'
                    })
                } else {
                    // 在 Web 环境中使用 window.open
                    window.open(customApkUrl, '_blank')
                }
            } catch (error) {
                console.error('打开下载链接失败:', error)
                await Toast.show({
                    text: '无法打开下载链接，请尝试复制链接手动下载',
                    duration: 'long',
                    position: 'center'
                })
            }
        }
    }

    // 手动下载 - 复制下载链接到剪贴板
    const handleManualDownload = async () => {
        if (customApkUrl) {
            try {
                await navigator.clipboard.writeText(customApkUrl)
                await Toast.show({
                    text: '下载链接已复制到剪贴板',
                    duration: 'short',
                    position: 'center'
                })
            } catch (error) {
                // 如果剪贴板 API 不可用，使用备用方法
                const textArea = document.createElement('textarea')
                textArea.value = customApkUrl
                document.body.appendChild(textArea)
                textArea.select()
                try {
                    document.execCommand('copy')
                    await Toast.show({
                        text: '下载链接已复制到剪贴板',
                        duration: 'short',
                        position: 'center'
                    })
                } catch (fallbackError) {
                    await Toast.show({
                        text: '复制失败，请手动复制链接',
                        duration: 'long',
                        position: 'center'
                    })
                }
                document.body.removeChild(textArea)
            }
        }
    }

    // 手动检查更新
    const handleCheckUpdate = async () => {
        setIsChecking(true)
        try {
            const data = await checkForUpdates()
            if (data && shouldUpdate(data.versionCode)) {
                setUpdateAvailable(true)
                setCustomApkUrl(data.apkUrl)
                setUpdateNotes(data.updateNotes)
                await Toast.show({
                    text: '发现新版本',
                    duration: 'short',
                    position: 'center'
                })
            } else {
                setUpdateAvailable(false)
                await Toast.show({
                    text: '当前已是最新版本',
                    duration: 'short',
                    position: 'center'
                })
            }
        } catch (error) {
            console.warn('检查更新失败:', error)
            setUpdateAvailable(false)
            await Toast.show({
                text: '检查更新失败，请稍后再试',
                duration: 'short',
                position: 'center'
            })
        } finally {
            setIsChecking(false)
        }
    }

    return (
        <IonPage>
            <IonHeader>
                <IonToolbar
                    style={{
                        '--background': '#FFFFFF',
                        '--border-width': '0 0 1px 0',
                        '--border-color': '#E5E5E5',
                    }}
                >
                    <IonButtons slot='start'>
                        <HeaderBackButton defaultHref='/settings' />
                    </IonButtons>
                    <IonTitle
                        style={{ color: '#191919', fontSize: '17px', fontWeight: '600' }}
                    >
                        版本更新
                    </IonTitle>
                </IonToolbar>
            </IonHeader>

            <IonContent>
                <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col">


                    {/* 主要内容区域 */}
                    <div className="flex-1 flex flex-col px-6 py-8 min-h-0">
                        {/* 当前版本信息 */}
                        <div className="mb-6 mt-10">
                            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    当前版本
                                </h3>
                                <div className="flex justify-between items-center">
                                    <div className="text-gray-700">
                                        <p className="text-lg font-medium">{version}</p>
                                        <p className="text-sm text-gray-500 mt-1">版本代码: {updateConfig.versionCode}</p>
                                    </div>
                                    <button
                                        onClick={handleCheckUpdate}
                                        disabled={isChecking}
                                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${isChecking
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-blue-50 text-blue-600 hover:bg-blue-100 active:scale-95'
                                            }`}
                                    >
                                        {isChecking ? '检查中...' : '检查更新'}
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* 更新内容区域 - 带滚动条 */}
                        {updateAvailable && updateNotes && (
                            <div className="mb-8" style={{ height: 'calc(100vh - 600px)' }}>
                                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 h-full flex flex-col">
                                    <div className="px-6 py-4 border-b border-gray-100 flex-shrink-0">
                                        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                            </div>
                                            更新内容
                                        </h3>
                                    </div>
                                    <div className="flex-1 overflow-y-auto px-6 py-4 min-h-0">
                                        <div className="text-gray-700 whitespace-pre-line leading-relaxed text-sm">
                                            {updateNotes}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* 如果没有更新或正在检查 */}
                        {(!updateAvailable || isChecking) && (
                            <div className="mb-8 flex items-center justify-center" style={{ height: 'calc(100vh - 400px)' }}>
                                <div className="text-center">
                                    <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
                                        {isChecking ? (
                                            <svg className="w-10 h-10 text-blue-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        ) : (
                                            <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                            </svg>
                                        )}
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                        {isChecking ? '正在检查更新...' : '当前已是最新版本'}
                                    </h3>
                                    <p className="text-gray-600 text-sm">
                                        {isChecking ? '请稍候...' : '您的应用已经是最新版本'}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* 按钮区域 - 仅在有更新时显示 */}
                        {updateAvailable && !isChecking && (
                            <div className="space-y-4">
                                {/* 主要更新按钮 */}
                                {customApkUrl && (
                                    <button
                                        onClick={handleCustomUpdate}
                                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center justify-center space-x-3"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                        </svg>
                                        <span>立即更新</span>
                                    </button>
                                )}
                                {!customApkUrl && updateType === 'immediate' && (
                                    <button
                                        onClick={handleImmediateUpdate}
                                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center justify-center space-x-3"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        <span>立即更新</span>
                                    </button>
                                )}
                                {!customApkUrl && updateType === 'flexible' && (
                                    <button
                                        onClick={handleFlexibleUpdate}
                                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center justify-center space-x-3"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707" />
                                        </svg>
                                        <span>后台更新</span>
                                    </button>
                                )}

                                {/* 手动下载按钮 */}
                                {customApkUrl && (
                                    <button
                                        onClick={handleManualDownload}
                                        className="w-full bg-white hover:bg-gray-50 text-gray-700 font-medium py-4 px-6 rounded-2xl transition-all duration-300 border-2 border-gray-200 hover:border-gray-300 flex items-center justify-center space-x-3 shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                        </svg>
                                        <span>复制下载链接</span>
                                    </button>
                                )}
                            </div>
                        )}
                    </div>

                    {/* 底部提示 */}
                    <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                        <div className="flex items-center justify-center space-x-2">
                            <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                            <p className="text-xs text-gray-600 text-center leading-relaxed">
                                为了您的使用体验和数据安全，建议及时更新到最新版本
                            </p>
                        </div>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )
}