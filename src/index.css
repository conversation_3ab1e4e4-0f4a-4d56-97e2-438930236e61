@tailwind base;
@tailwind components;
@tailwind utilities;

/* 确保全屏高度 */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

#root {
  --ion-background-color: #f5f5f5;
  height: 100%;
  width: 100%;
}

img {
  @apply select-none;
  -webkit-user-drag: none;
}

/* Mermaid 图表样式 - 去掉边框和背景 */
.mermaid-container {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.mermaid-container svg {
  background: transparent !important;
  border: none !important;
  outline: none !important;
}

.mermaid-container div[data-mermaid] {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 移动端优化 */
@media (max-width: 768px) {
  html,
  body {
    height: 100vh;
    height: 100dvh; /* 动态视口高度，支持移动端 */
  }

  #root {
    height: 100vh;
    height: 100dvh;
  }

  /* 确保所有元素不超出视口宽度 */
  * {
    box-sizing: border-box;
    max-width: 100vw;
  }
}

/* todo: 好像是 tailwind3 导致的浅蓝色激活背景，没找到*/
*:active {
  -webkit-tap-highlight-color: transparent; /* 针对移动端点击高亮 */
}

/* 用户消息气泡样式优化 */
.user-message-content {
  /* 确保文字在合适的地方换行 */
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  
  /* 防止单个字符换行 */
  word-spacing: normal;
  
  /* 确保中文字符不会在奇怪的地方断开 */
  hyphens: none;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
}

/* 用户消息气泡容器样式 */
.group-\[\.mine\] .user-message-content {
  /* 设置合理的最大宽度，避免过宽 */
  max-width: 280px;
  
  /* 确保内容自适应宽度 */
  width: fit-content;
  
  /* 防止内容溢出 */
  overflow-wrap: break-word;
  word-break: break-word;
}

/* 表格滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 移动端表格滚动优化 */
@media (max-width: 768px) {
  .overflow-x-auto::-webkit-scrollbar {
    height: 3px; /* 移动端滚动条高度 */
  }
  
  .overflow-x-auto::-webkit-scrollbar-thumb {
    background: #9ca3af; /* 移动端颜色深一点，更明显 */
    border-radius: 2px;
  }
  
  .overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
  
  /* 移动端表格容器优化 */
  .table-container-mobile {
    -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
    scroll-behavior: smooth;
    overscroll-behavior-x: contain; /* 防止过度滚动 */
  }
}


.mermaid-error {
  display: none !important;
}

