import { updateConfig } from '../data/updateConfig'

interface UpdateInfo {
  versionCode: number
  versionName: string
  updateNotes: string
  apkUrl: string
}

/**
 * 检查应用更新
 * 使用通用代理接口避免 CORS 问题
 */
export async function checkForUpdates(): Promise<UpdateInfo | null> {
  console.log('🚀 开始检查更新...');
  
    const directUrl = updateConfig.baseUrl + 'update.json?time=' + new Date().getTime() ;
    console.log(`🔍 直接请求: ${directUrl}`);
    
    try {
      const response = await fetch(directUrl, {
        method: 'GET',
        mode: 'cors', // 确保使用 CORS 模式
        headers: {
          'Accept': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 直接请求成功:', data);
        return data;
      } else {
        console.warn(`❌ 直接请求失败: ${response.status}`);
      }
    } catch (error) {
      console.warn('直接请求失败:', error);
    }

}

/**
 * 检查是否需要更新
 */
export function shouldUpdate(serverVersionCode: number): boolean {
  const should = serverVersionCode > updateConfig.versionCode;
  console.log(`🔍 版本比较: 服务器 ${serverVersionCode} vs 本地 ${updateConfig.versionCode} -> ${should ? '需要更新' : '无需更新'}`);
  return should;
} 