// Capacitor 插件存根文件
// 在开发模式下提供空的实现，避免导入错误

// AppUpdate 存根
export const AppUpdate = {
  getAppUpdateInfo: () => Promise.resolve({ updateAvailability: 'UPDATE_NOT_AVAILABLE' }),
  performImmediateUpdate: () => Promise.resolve(),
  startFlexibleUpdate: () => Promise.resolve(),
  completeFlexibleUpdate: () => Promise.resolve(),
  openAppStore: () => Promise.resolve(),
}

export const AppUpdateAvailability = {
  UPDATE_NOT_AVAILABLE: 'UPDATE_NOT_AVAILABLE',
  UPDATE_AVAILABLE: 'UPDATE_AVAILABLE',
  UPDATE_IN_PROGRESS: 'UPDATE_IN_PROGRESS',
}

// App 存根
export const App = {
  getInfo: () => Promise.resolve({ name: 'Tina Chat', version: '1.0.0' }),
  getLaunchUrl: () => Promise.resolve({ url: null }),
  getState: () => Promise.resolve({ isActive: true }),
  exitApp: () => Promise.resolve(),
  canOpenUrl: () => Promise.resolve({ value: false }),
  openUrl: () => Promise.resolve({ completed: false }),
}

// Browser 存根
export const Browser = {
  open: () => Promise.resolve(),
  close: () => Promise.resolve(),
  addListener: () => Promise.resolve(),
  removeAllListeners: () => Promise.resolve(),
}

// Haptics 存根
export const Haptics = {
  impact: () => Promise.resolve(),
  notification: () => Promise.resolve(),
  selection: () => Promise.resolve(),
  vibrate: () => Promise.resolve(),
}

// Keyboard 存根
export const Keyboard = {
  show: () => Promise.resolve(),
  hide: () => Promise.resolve(),
  setAccessoryBarVisible: () => Promise.resolve(),
  setScroll: () => Promise.resolve(),
  setResizeMode: () => Promise.resolve(),
  setStyle: () => Promise.resolve(),
  addListener: () => Promise.resolve(),
  removeAllListeners: () => Promise.resolve(),
}

// StatusBar 存根
export const StatusBar = {
  show: () => Promise.resolve(),
  hide: () => Promise.resolve(),
  setStyle: () => Promise.resolve(),
  setBackgroundColor: () => Promise.resolve(),
  setOverlaysWebView: () => Promise.resolve(),
}

// Toast 存根
export const Toast = {
  show: () => Promise.resolve(),
}

// 默认导出
export default {
  AppUpdate,
  AppUpdateAvailability,
  App,
  Browser,
  Haptics,
  Keyboard,
  StatusBar,
  Toast,
} 