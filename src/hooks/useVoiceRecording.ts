import { useState, useRef, useCallback, useEffect } from 'react'
import { FunASRSDK } from '@/tina/lib/funasr-sdk'

declare global {
  interface Window {
    FunASRSDK: any
  }
}

export interface VoiceRecordingState {
  isRecording: boolean
  isConnected: boolean
  hasPermission: boolean
  error: string | null
}

export const useVoiceRecording = (serverUrl?: string) => {
  const [state, setState] = useState<VoiceRecordingState>({
    isRecording: false,
    isConnected: false,
    hasPermission: false, // 初始化时设为 false，不检查权限
    error: null,
  })

  const [audioLevels, setAudioLevels] = useState<number[]>(Array(15).fill(0))
  const sdkRef = useRef<FunASRSDK | null>(null)
  const recognitionResultRef = useRef<string>('')
  const isCancelledRef = useRef<boolean>(false) // 添加取消标志

  // 处理音频数据，计算音量级别
  const processAudioForWaveform = useCallback((audioData: Float32Array | number[]) => {
    // 如果是Android原生传递的音频级别数组，直接使用
    if (Array.isArray(audioData)) {
      setAudioLevels(audioData)
      return
    }
    
    // Web版本的音频数据处理
    const bufferSize = audioData.length
    const sampleRate = 44100 // 假设采样率
    const segmentSize = Math.floor(bufferSize / 15) // 分成15段
    const levels: number[] = []

    for (let i = 0; i < 15; i++) {
      const start = i * segmentSize
      const end = Math.min(start + segmentSize, bufferSize)
      
      // 计算该段的RMS (均方根)
      let sum = 0
      for (let j = start; j < end; j++) {
        sum += audioData[j] * audioData[j]
      }
      const rms = Math.sqrt(sum / (end - start))
      
      // 转换为0-100的范围，并应用一些放大
      const level = Math.min(100, Math.max(20, rms * 3000))
      levels.push(level)
    }

    setAudioLevels(levels)
  }, [])

  // 初始化SDK
  const initSDK = useCallback(() => {
    try {
      if (!FunASRSDK.isSupported()) {
        setState(prev => ({ ...prev, error: '浏览器不支持语音录制功能' }))
        return false
      }

      sdkRef.current = new FunASRSDK({
        onResult: (data) => {
          console.log('识别结果:', data)
          // 检查是否已被取消
          if (isCancelledRef.current) {
            console.log('录音已取消，忽略识别结果')
            return
          }
          
          // 只处理最终结果且包含文本内容的识别结果
          if (data && data.text != null && data.is_final ) {
            const text = data.text.trim()
            recognitionResultRef.current = text
            console.log('更新最终识别结果:', text)
            
            // 立即发送识别结果
            if (onResultRef.current && !isCancelledRef.current) {
              console.log('立即发送识别结果:', text)
              onResultRef.current(text)
            }
          }
        },
        onError: (error) => {
          console.error('语音识别错误:', error)
          setState(prev => ({ ...prev, error }))
        },
        onStatusChange: (status) => {
          console.log('SDK状态变化:', status)
          setState(prev => {
            const newState = { ...prev }
            
            switch (status) {
              case 'connected':
                newState.isConnected = true
                newState.error = null
                break
              case 'recording':
                newState.isRecording = true
                recognitionResultRef.current = ''
                isCancelledRef.current = false // 重置取消标志
                break
              case 'stopped':
                newState.isRecording = false
                // 清空识别结果缓存
                recognitionResultRef.current = ''
                // 重置音频级别
                setAudioLevels(Array(15).fill(0))
                break
              case 'disconnected':
                newState.isConnected = false
                break
            }
            
            return newState
          })
        },
        onAudioData: processAudioForWaveform,
      })

      return true
    } catch (error) {
      console.error('初始化SDK失败:', error)
      setState(prev => ({ ...prev, error: '初始化语音SDK失败' }))
      return false
    }
  }, [serverUrl, processAudioForWaveform])

  const onResultRef = useRef<((text: string) => void) | null>(null)

  // 设置结果回调
  const setOnResult = useCallback((callback: (text: string) => void) => {
    onResultRef.current = callback
  }, [])

  // 开始录音
  const startRecording = useCallback(async () => {
    try {
      if (!sdkRef.current) {
        const initialized = initSDK()
        if (!initialized) {
          return false
        }
      }

      // 确保SDK已完全初始化（等待Android原生SDK初始化完成）
      if (sdkRef.current && typeof sdkRef.current.ensureInitialized === 'function') {
        await sdkRef.current.ensureInitialized()
      }

      // 重置取消标志
      isCancelledRef.current = false
      console.log('重置录音取消标志')
      const success = await sdkRef.current!.startRecording()
      console.log('开始录音成功:', success)
      if (success) {
        setState(prev => ({ 
          ...prev, 
          isRecording: true, 
          error: null,
          hasPermission: true 
        }))
      }
      
      return success
    } catch (error) {
      console.error('开始录音失败:', error)
      setState(prev => ({ ...prev, error: '开始录音失败' }))
      return false
    }
  }, [initSDK])

  // 停止录音
  const stopRecording = useCallback(async () => {
    try {
      if (!sdkRef.current) {
        return false
      }

      const success = await sdkRef.current.stopRecording()
      
      if (success) {
        setState(prev => ({ ...prev, isRecording: false }))
        setAudioLevels(Array(15).fill(0)) // 重置音频级别
      }
      
      return success
    } catch (error) {
      console.error('停止录音失败:', error)
      setState(prev => ({ ...prev, error: '停止录音失败' }))
      return false
    }
  }, [])

  // 取消录音（不发送消息）
  const cancelRecording = useCallback(async () => {
    try {
      if (!sdkRef.current) {
        return false
      }

      // 设置取消标志，阻止后续的识别结果处理
      isCancelledRef.current = true
      console.log('设置录音取消标志')

      const success = await sdkRef.current.stopRecording()
      
      if (success) {
        setState(prev => ({ ...prev, isRecording: false }))
        setAudioLevels(Array(15).fill(0)) // 重置音频级别
        // 清空识别结果缓存
        recognitionResultRef.current = ''
      }
      
      return success
    } catch (error) {
      console.error('取消录音失败:', error)
      setState(prev => ({ ...prev, error: '取消录音失败' }))
      return false
    }
  }, [])

  // 断开连接
  const disconnect = useCallback(() => {
    if (sdkRef.current) {
      sdkRef.current.disconnect()
      sdkRef.current = null
      setState({
        isRecording: false,
        isConnected: false,
        hasPermission: false,
        error: null,
      })
      setAudioLevels(Array(15).fill(0))
    }
  }, [])

  // 清除错误
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // 检查浏览器支持
  const isSupported = useCallback(() => {
    return FunASRSDK.isSupported()
  }, [])

  // 清理资源
  useEffect(() => {
    return () => {
      if (sdkRef.current) {
        sdkRef.current.disconnect()
      }
    }
  }, [])

  return {
    ...state,
    audioLevels, // 暴露音频级别数据
    startRecording,
    stopRecording,
    cancelRecording,
    disconnect,
    clearError,
    isSupported,
    setOnResult,
  }
} 