import { Suspense, lazy } from 'react'
import { IonApp, IonRouterOutlet } from '@ionic/react'
import { IonReactHashRouter } from '@ionic/react-router'
import { Route } from 'react-router-dom'
import LoadingPage from '@/components/LoadingPage'
// 页面组件
import HomePage from './pages/HomePage'
import TestPage from './pages/TestPage'
import TextTestPage from './pages/TextTestPage.tsx'
import TinaTaskTestPage from './pages/TinaTaskTestPage'
import OnboardingPage from './pages/onboarding'
import MobileSettings from './pages/settings'
import SplashPage from './pages/splash'
import VersionUpdatePage from './pages/version-update'
import TaskDetail from './pages/task-detail/index'
import TestConfirmationDialog from './pages/test-confirmation-dialog'
import TestUserMaybeSayPage from './pages/test-user-maybe-say'
import Conversation from './pages/wechat/conversation'


function App() {
  return (
    <IonApp>
      <IonReactHashRouter>
        <IonRouterOutlet>
          <Route exact path='/' component={SplashPage} />
          <Route exact path='/test' component={TestPage} />
          <Route exact path='/text-component-test' component={TextTestPage} />
          <Route exact path='/tina-task-test' component={TinaTaskTestPage} />
          <Route
            exact
            path='/test-confirmation-dialog'
            component={TestConfirmationDialog}
          />
          <Route
            exact
            path='/test-user-maybe-say'
            component={TestUserMaybeSayPage}
          />

          <Route exact path='/home' component={HomePage} />
          <Route exact path='/onboarding' component={OnboardingPage} />
          <Route exact path='/task-detail/:taskId' component={TaskDetail} />
          <Route exact path='/settings' component={MobileSettings} />
          <Route exact path='/version-update' component={VersionUpdatePage} />
          <Route
            exact
            path='/conversation/:id'
            render={() => (
              <Suspense fallback={<LoadingPage />}>
                <Conversation />
              </Suspense>
            )}
          />
        </IonRouterOutlet>
      </IonReactHashRouter>
    </IonApp>
  )
}

export default App
