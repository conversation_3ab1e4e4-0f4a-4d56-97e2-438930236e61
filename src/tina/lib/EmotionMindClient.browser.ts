// ==================== 类型定义 ====================
// 基础响应类型
import { HistoryMessage } from '@/tina/lib/types.ts'
import { performUnifiedLogout, shouldPerformUnifiedLogout } from './auth'
import { EventSourceController, EventSourcePlus } from 'event-source-plus'

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp: string
}

// 消息类型枚举
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  FILE = 'file',
  SYSTEM = 'system',
  PROFILE = 'profile',
}

// 直接上传响应接口
export interface DirectUploadResponse {
  objectName: string // 文件在存储中的唯一标识
  originalFilename: string // 原始文件名
  fileSize: number // 文件大小
  contentType: string // 文件类型
  accessUrl: string // 安全访问URL
  etag: string // 文件ETag
  message: string // 状态消息
}

// 直接上传请求接口
export interface DirectUploadRequest {
  filename: string
  contentType: string
  pathPrefix?: string // 可选的自定义路径前缀
  fileData: string // Base64编码的文件数据
}

// EmotionMind 服务相关类型
export interface SendMessageRequest {
  user_id: string
  content: string
  message_type: MessageType
  metadata?: Record<string, any>
}

export interface SendMessageResponse {
  message_id: string
  response: string
  metadata?: Record<string, any>
}

// 设置用户 Suffix 请求
export interface SetUserSuffixRequest {
  suffix_name: string
}

// 设置用户 Suffix 响应
export interface SetUserSuffixResponse {
  user_id: string
  suffix_name: string
  success: boolean
  message: string
}

export interface StreamMessage {
  type:
    | 'connected'
    | 'heartbeat'
    | 'llm_response'
    | 'error'
    | 'message'
    | 'user_message'
    | 'tool_callback'
    | 'tool_message'
    | 'task'
  content?: string
  delta?: boolean
  finished?: boolean
  message_id?: string
  chunk_id?: number
  timestamp: string
  device_id?: string
  error?: string
  metadata?: Record<string, any>
  message_type?: string
  tool_name?: string
  tool_called?: boolean
}

export interface TaskMetadata {
  content: string
  session_id: string
  step: number
  timestamp: number
  xml_type:
    | 'plan'
    | 'tools_call'
    | 'task_completed'
    | 'change_plan'
    | 'ask_user_info'
}

// 客户端配置
export interface ClientConfig {
  baseURL: string
  timeout: number
  retries: number
  debug: boolean
  userFilesBaseURL: string
}

// 消息流回调接口
export interface MessageStreamCallbacks {
  onConnected?: (data: any) => void
  onHeartbeat?: (data: any) => void
  onUserMessage?: (data: any) => void
  onLLMResponse?: (data: any) => void
  onError?: (data: any) => void
  onToolCallback?: (data: any) => void
  onToolMessage?: (data: any) => void
  onTask?: (data: StreamMessage) => void
  onMessage?: (message: StreamMessage) => void
  onOpen?: () => void
  onStreamError?: (error: any) => void
  onClose?: () => void
}

// 消息流连接接口
export interface MessageStreamConnection {
  close(): void
  isConnected(): boolean
  getReadyState(): 'connecting' | 'connected' | 'closed' | 'error'
}

// ==================== 客户端实现 ====================

export class EmotionMindClient {
  private config: ClientConfig
  private authHeaders: Record<string, string> = {}

  constructor(config: Partial<ClientConfig> = {}) {
    this.config = {
      // baseURL: 'http://localhost:8380/emotionmind',
      baseURL: 'https://tina-test.bfbdata.com/gateway/emotionmind',
      timeout: 30000,
      retries: 3,
      debug: false,
      userFilesBaseURL: 'https://tina-test.bfbdata.com/gateway/userfiles',
      ...config,
    }
  }

  // ==================== 认证方法 ====================

  /**
   * 设置认证头
   */
  setAuthHeaders(headers: Record<string, string>) {
    this.authHeaders = { ...this.authHeaders, ...headers }
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string) {
    this.authHeaders['Authorization'] = `Bearer ${token}`
  }

  /**
   * 清除认证信息
   */
  clearAuth() {
    this.authHeaders = {}
  }

  // ==================== 辅助方法 ====================

  private async fetchWithRetry(
    url: string,
    options: RequestInit = {},
  ): Promise<Response> {
    const { timeout, retries } = this.config

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout)

        const response = await fetch(url, {
          ...options,
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            ...this.authHeaders,
            ...options.headers,
          },
        })

        clearTimeout(timeoutId)

        if (this.config.debug) {
          console.log(
            `[${new Date().toISOString()}] ${options.method || 'GET'} ${url} -> ${response.status}`,
          )
        }

        if (!response.ok) {
          const httpError = new Error(
            `HTTP ${response.status}: ${response.statusText}`,
          )

          // 检查是否需要执行统一登出
          if (shouldPerformUnifiedLogout(response.status, httpError)) {
            console.log(
              '🚪 [EmotionMindClient] 检测到需要登出的HTTP错误:',
              response.status,
            )
            // 异步执行登出，不阻塞当前错误抛出
            performUnifiedLogout(
              `EmotionMind HTTP ${response.status}: ${response.statusText}`,
            ).catch((logoutError) => {
              console.error(
                '🚪 [EmotionMindClient] 统一登出执行失败:',
                logoutError,
              )
            })
          }

          throw httpError
        }

        return response
      } catch (error) {
        if (this.config.debug) {
          console.log(
            `[${new Date().toISOString()}] 请求失败 (尝试 ${attempt + 1}/${retries + 1}):`,
            error,
          )
        }

        // 检查是否为认证相关错误，如果是则不重试
        if (shouldPerformUnifiedLogout(undefined, error)) {
          console.log('🚪 [EmotionMindClient] 检测到认证错误，停止重试')
          throw error
        }

        if (attempt === retries) {
          throw error
        }

        // 等待后重试
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * Math.pow(2, attempt)),
        )
      }
    }

    throw new Error('所有重试都失败了')
  }

  // ==================== EmotionMind 服务 API ====================

  /**
   * 检查 EmotionMind 服务健康状态
   */
  async checkEmotionMindHealth(): Promise<any> {
    try {
      const response = await this.fetchWithRetry(
        `${this.config.baseURL}/health`,
      )
      return await response.json()
    } catch (error) {
      throw new Error(`EmotionMind health check failed: ${error}`)
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const response = await this.fetchWithRetry(
        `${this.config.baseURL}/api/v1/message/send`,
        {
          method: 'POST',
          body: JSON.stringify(request),
        },
      )

      const data: ApiResponse<SendMessageResponse> = await response.json()
      return data.data!
    } catch (error) {
      throw new Error(`Send message failed: ${error}`)
    }
  }

  /**
   * 查询历史消息（按时间戳）
   */
  async queryHistoryByTimestamp(request: {
    user_id: string
    timestamp?: number
    limit?: number
    offset?: number
  }): Promise<{
    messages: Array<HistoryMessage>
    total: number
  }> {
    try {
      const response = await this.fetchWithRetry(
        `${this.config.baseURL}/api/v1/message/history`,
        {
          method: 'POST',
          body: JSON.stringify(request),
        },
      )

      const data = await response.json()
      return data.data
    } catch (error) {
      throw new Error(`Query history by timestamp failed: ${error}`)
    }
  }

  /**
   * 设置用户 Suffix
   * @param suffixName 要设置的 suffix 名称
   * @param userIdHeader 可选的用户ID，如果不提供则需要在请求头中设置 X-User-ID
   */
  async setUserSuffix(
    suffixName: string,
    userIdHeader?: string,
  ): Promise<SetUserSuffixResponse> {
    try {
      const request: SetUserSuffixRequest = {
        suffix_name: suffixName,
      }

      const response = await this.fetchWithRetry(
        `${this.config.baseURL}/api/v1/message/suffix`,
        {
          method: 'POST',
          body: JSON.stringify(request),
        },
      )

      if (this.config.debug) {
        console.log(
          `[${new Date().toISOString()}] 用户 Suffix 设置成功: ${suffixName}`,
        )
      }

      const data: ApiResponse<SetUserSuffixResponse> = await response.json()
      return data.data!
    } catch (error) {
      throw new Error(`Set user suffix failed: ${error}`)
    }
  }

  /**
   * 用户登录后的初始化设置
   * 自动设置 agentsuffix 为 "normal"
   * @param userId 用户ID
   */
  async initializeUserAfterLogin(userId: string): Promise<void> {
    try {
      if (this.config.debug) {
        console.log(
          `[${new Date().toISOString()}] 开始用户登录后初始化: ${userId}`,
        )
      }

      // 设置 agentsuffix 为 "normal"
      await this.setUserSuffix('wxlike', userId)

      if (this.config.debug) {
        console.log(
          `[${new Date().toISOString()}] 用户登录后初始化完成: ${userId}`,
        )
      }
    } catch (error) {
      console.error(`用户登录后初始化失败: ${error}`)
      throw error
    }
  }

  /**
   * 创建消息流连接
   */
  createMessageStream(
    userId: string,
    deviceId: string,
    callbacks: MessageStreamCallbacks = {},
    headers: Record<string, string> = {}
  ): MessageStreamConnection {
    const url = `${this.config.baseURL}/api/v1/message/stream?user_id=${userId}&device_id=${deviceId}`

    // 连接状态管理
    let controller: EventSourceController| null = null
    let readyState: 'connecting' | 'connected' | 'closed' | 'error' =
      'connecting'

    // 调试日志辅助函数
    const debugLog = (message: string, ...args: any[]) => {
      if (this.config.debug) {
        console.log(`[${new Date().toISOString()}] [SSE] ${message}`, ...args)
      }
    }

    // 消息处理函数
    const processMessage = (data: string) => {
      // 忽略 [DONE] 标记
      if (data === '[DONE]') {
        debugLog('收到流结束标记')
        return
      }

      try {
        const message: StreamMessage = JSON.parse(data)
        debugLog('收到消息:', message.type)

        // 统一回调
        callbacks.onMessage?.(message)

        // 按类型分发回调
        const messageHandlers = {
          connected: callbacks.onConnected,
          heartbeat: callbacks.onHeartbeat,
          user_message: callbacks.onUserMessage,
          llm_response: callbacks.onLLMResponse,
          error: callbacks.onError,
          tool_message: callbacks.onToolMessage,
          tool_callback: callbacks.onToolCallback,
          task: callbacks.onTask,
        } as const

        const handler =
          messageHandlers[message.type as keyof typeof messageHandlers]
        if (handler) {
          handler(message)
        } else {
          debugLog('未知消息类型:', message.type)
        }
      } catch (parseError) {
        console.error('[SSE] 解析消息失败:', parseError, '原始数据:', data)
      }
    }

    // 连接状态更新函数
    const updateReadyState = (newState: typeof readyState) => {
      if (readyState !== newState) {
        readyState = newState
        debugLog('连接状态变更:', newState)
      }
    }

    // 初始化连接
    const initializeConnection = () => {
      try {
        debugLog('开始初始化 SSE 连接:', url)

        // 构建请求头
        const requestHeaders = {
          ...this.authHeaders,
          ...headers
        }

        // 创建 EventSource 实例
        const eventSource = new EventSourcePlus(url, {
          headers: requestHeaders,
          maxRetryCount: 5,
          maxRetryInterval: 30000,
          retryStrategy: 'always' as const
        })

        // 开始监听
        controller = eventSource.listen({
          onRequest: () => {
            debugLog('发起 SSE 请求')
            updateReadyState('connecting')
          },

          onResponse: ({ response }) => {
            if (response.ok) {
              updateReadyState('connected')
              debugLog('SSE 连接建立成功')
              callbacks.onOpen?.()
            } else {
              debugLog('SSE 响应错误:', response.status, response.statusText)
            }
          },

          onMessage: (event) => {
            processMessage(event.data)
          },

          onRequestError: ({ error }) => {
            updateReadyState('error')
            debugLog('请求错误:', error.message)
            callbacks.onStreamError?.(error)
          },

          onResponseError: ({ response, error }) => {
            updateReadyState('error')
            debugLog('响应错误:', response?.status, error?.message)
            callbacks.onStreamError?.(
              error || new Error(`HTTP ${response?.status}`)
            )
          }
        })

        // 注册中止回调
        controller.onAbort(() => {
          updateReadyState('closed')
          debugLog('连接已关闭')
          callbacks.onClose?.()
        })
      } catch (error: any) {
        updateReadyState('error')
        debugLog('初始化连接失败:', error.message)
        callbacks.onStreamError?.(error)
      }
    }

    // 立即开始连接
    initializeConnection()

    // 返回连接控制接口
    return {
      close: () => {
        debugLog('手动关闭 SSE 连接')
        try {
          controller?.abort()
        } catch (error) {
          debugLog('关闭连接时出错:', error)
        }
        controller = null
      },

      isConnected(): boolean {
        return readyState === 'connected'
      },

      getReadyState(): 'connecting' | 'connected' | 'closed' | 'error' {
        return readyState
      },
    }
  }


  /**
   * 检查服务健康状态（别名方法）
   */
  async checkHealth(): Promise<any> {
    return this.checkEmotionMindHealth()
  }

  /**
   * 设置调试模式
   */
  setDebug(debug: boolean) {
    this.config.debug = debug
  }

  /**
   * 获取客户端配置
   */
  getConfig(): ClientConfig {
    return { ...this.config }
  }

  /**
   * 关闭消息流连接
   */
  closeMessageStream(connection: MessageStreamConnection) {
    connection.close()
  }

  /**
   * 结束聊天会话
   */
  async endSession(): Promise<void> {
    try {
      await this.fetchWithRetry(`${this.config.baseURL}/chat/end`, {
        method: 'POST',
        headers: this.authHeaders,
      })
    } catch (error) {
      console.error('结束会话失败:', error)
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 将文件转换为 Base64 字符串
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      reader.readAsDataURL(file)
    })
  }

  /**
   * 压缩图片文件
   */
  private compressImage(
    file: File,
    maxShortSide: number = 1000,
    quality: number = 0.8,
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      // 检查是否为图片文件
      if (!file.type.startsWith('image/')) {
        resolve(file)
        return
      }

      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        const { width, height } = img
        const shortSide = Math.min(width, height)

        // 如果最短边小于等于限制，且文件大小合理，直接返回原文件
        if (shortSide <= maxShortSide && file.size <= 1 * 1024 * 1024) {
          // 1MB
          resolve(file)
          return
        }

        // 计算新的尺寸
        let newWidth = width
        let newHeight = height

        if (shortSide > maxShortSide) {
          const scale = maxShortSide / shortSide
          newWidth = Math.round(width * scale)
          newHeight = Math.round(height * scale)
        }

        // 设置画布尺寸
        canvas.width = newWidth
        canvas.height = newHeight

        // 绘制压缩后的图片
        ctx!.drawImage(img, 0, 0, newWidth, newHeight)

        // 转换为 Blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('图片压缩失败'))
              return
            }

            // 创建新的 File 对象
            const compressedFile = new File(
              [blob],
              file.name.replace(/\.(png|webp|bmp|tiff?)$/i, '.jpg'),
              {
                type: 'image/jpeg',
                lastModified: Date.now(),
              },
            )

            if (this.config.debug) {
              console.log(`[${new Date().toISOString()}] 图片压缩完成:
                原始尺寸: ${width}x${height} (${(file.size / 1024 / 1024).toFixed(2)}MB)
                压缩后: ${newWidth}x${newHeight} (${(compressedFile.size / 1024 / 1024).toFixed(2)}MB)`)
            }

            resolve(compressedFile)
          },
          'image/jpeg',
          quality,
        )
      }

      img.onerror = () => {
        reject(new Error('图片加载失败'))
      }

      // 创建图片URL并加载
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 直接上传文件
   */
  async directUpload(
    request: DirectUploadRequest,
  ): Promise<DirectUploadResponse> {
    try {
      const headers: any = {
        'Content-Type': 'application/json',
        ...this.authHeaders,
      }

      const response = await this.fetchWithRetry(
        `${this.config.userFilesBaseURL}/api/v1/uploads/direct`,
        {
          method: 'POST',
          headers,
          body: JSON.stringify(request),
        },
      )

      const data = await response.json()
      console.log('🔍 directUpload 原始响应:', data)

      // 根据实际响应结构返回数据
      // 如果响应有 data 字段，返回 data，否则直接返回响应
      return data.data || data
    } catch (error) {
      console.error('🚨 directUpload 错误:', error)
      throw new Error(`直接上传失败: ${error}`)
    }
  }

  /**
   * 上传文件（便捷方法）
   */
  async uploadFile(
    file: File,
    pathPrefix?: string,
  ): Promise<DirectUploadResponse> {
    try {
      // 对图片文件进行压缩处理
      let processedFile = file
      if (file.type.startsWith('image/')) {
        processedFile = await this.compressImage(file)
      }

      const fileData = await this.fileToBase64(processedFile)

      const request: DirectUploadRequest = {
        filename: processedFile.name,
        contentType: processedFile.type,
        pathPrefix,
        fileData: fileData.split(',')[1], // 移除 data:type;base64, 前缀
      }

      return await this.directUpload(request)
    } catch (error) {
      throw new Error(`文件上传失败: ${error}`)
    }
  }
}
