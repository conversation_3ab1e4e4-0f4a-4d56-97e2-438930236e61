import {
  DirectUploadResponse,
  EmotionMindClient,
  MessageStreamConnection,
  MessageType,
  SendMessageRequest,
  StreamMessage,
} from '@/tina/lib/EmotionMindClient.browser'
import { GATEWAY_URL } from '@/tina/lib/casdoor'
import { useAuthStore } from '@/tina/stores/authStore'

// 连接状态类型
export type ConnectionStatus =
  | 'connecting'
  | 'connected'
  | 'disconnected'
  | 'error'

// 回调函数类型定义
export interface ChatServiceManagerCallbacks {
  onConnectionStatusChange?: (status: ConnectionStatus) => void
  onLLMResponse?: (message: StreamMessage) => void
  onUserMessage?: (message: StreamMessage) => void
  onError?: (error: string) => void
  onToolMessage?: (data: StreamMessage) => void
  onToolCallback?: (data: StreamMessage) => void
  onTask?: (data: StreamMessage) => void
}

// 聊天服务管理器类
class ChatServiceManager {
  private static instance: ChatServiceManager
  private currentUserId: string = ''
  private callbacks: ChatServiceManagerCallbacks = {}
  private connectionStatus: ConnectionStatus = 'disconnected'

  // EmotionMindClient相关
  private client: EmotionMindClient
  private connection: MessageStreamConnection | null = null
  private readonly deviceId: string = ''

  // 自动重连相关 - 保持与原始逻辑一致
  private autoReconnect: boolean = true
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 2 // 原始逻辑：最多2次尝试
  private reconnectTimeouts: NodeJS.Timeout[] = []

  // 心跳检测相关
  private lastHeartbeatTime: number = 0
  private readonly heartbeatTimeoutMs: number = 3 * 60 * 1000 // 3分钟

  private constructor() {
    // 初始化EmotionMindClient
    const defaultBaseURL = import.meta.env.VITE_API_URL
      ? `${import.meta.env.VITE_API_URL}/emotionmind`
      : 'http://localhost:8380/emotionmind'
    this.client = new EmotionMindClient({
      baseURL: defaultBaseURL,
      debug: false,
      timeout: 30000,
      retries: 3,
    })

    // 生成设备ID
    this.deviceId = 'mobile_' + Math.random().toString(36).substring(2, 11)
  }

  // 获取单例实例
  static getInstance(): ChatServiceManager {
    if (!ChatServiceManager.instance) {
      ChatServiceManager.instance = new ChatServiceManager()
    }
    return ChatServiceManager.instance
  }

  // 初始化聊天服务
  initialize(callbacks: ChatServiceManagerCallbacks) {
    const timestamp = new Date().toISOString()
    const stack =
      new Error().stack?.split('\n').slice(1, 4).join('\n') || 'No stack'
    console.log(`🔍 [ChatServiceManager.initialize] 开始初始化 ${timestamp}`)
    console.log(`📍 调用栈:\n${stack}`)
    console.log(`🔗 当前连接状态: ${this.connectionStatus}`)
    console.log(`👤 当前用户ID: ${this.currentUserId}`)
    console.log(`🔌 连接对象存在: ${!!this.connection}`)

    // 总是更新回调函数，无论连接状态如何
    const previousCallbacks = this.callbacks
    this.callbacks = callbacks
    console.log('🔄 [ChatServiceManager.initialize] 回调函数已更新')

    // 如果已经存在连接对象且用户ID相同，只更新回调函数
    if (this.connection) {
      const authState = useAuthStore.getState().auth
      const userId =
        authState.userId || authState.user?.name || authState.user?.id
      if (this.currentUserId === userId) {
        console.log(
          '✅ [ChatServiceManager.initialize] 连接已存在，回调函数已更新',
        )
        return
      }
    }

    const authState = useAuthStore.getState().auth
    if (!authState.isLoggedIn()) {
      throw new Error('用户未登录')
    }

    const userId =
      authState.userId || authState.user?.name || authState.user?.id
    if (!userId) {
      throw new Error('无法获取用户ID')
    }

    console.log(`👤 新用户ID: ${userId}`)

    // 如果用户ID发生变化，需要重新连接并清空缓存
    if (this.currentUserId !== userId) {
      console.log('🔄 用户ID变化，断开旧连接')
      this.disconnect()
      this.currentUserId = userId
    }
    this.client.setAuthToken(authState.token || '')

    try {
      this.client.initializeUserAfterLogin(userId)
    } catch (error) {
      console.error('设置用户 suffix 失败', error)
    }

    try {
      console.log('🚀 创建新的消息流连接')
      this.connection = this.client.createMessageStream(userId, this.deviceId, {
        onConnected: (_message) => {
          console.log('🎉 连接已建立')
          this.updateConnectionStatus('connected')
          // 连接成功时重置重连计数器
          this.reconnectAttempts = 0
          this.autoReconnect = true
          // 连接建立时初始化心跳时间
          this.lastHeartbeatTime = Date.now()
        },
        onHeartbeat: (_message) => {
          // 记录心跳时间
          this.lastHeartbeatTime = Date.now()
          if (this.client.getConfig().debug) {
            console.log(
              `💓 [ChatServiceManager] 收到心跳包: ${new Date(this.lastHeartbeatTime).toISOString()}`,
            )
          }
        },
        onUserMessage: (message) => {
          // 把消息内容结尾的 <send_time> 去掉
          const cleanedMessage = {
            ...message,
            content: message.content
              .replace(/<send_time>.*?<\/send_time>/, '')
              .replace(/<voice_message>(.*?)<\/voice_message>/, '$1'),
          }
          this.callbacks.onUserMessage?.(cleanedMessage)
        },
        onLLMResponse: (message) => {
          this.handleLLMResponse(message)
        },
        onError: (message) => {
          this.callbacks.onError?.(message.error || '未知错误')
        },
        onStreamError: (error) => {
          this.updateConnectionStatus('error')
          this.callbacks.onError?.(error.message)
          this.handleConnectionLoss() // 错误时也触发重连，保持与原始逻辑一致
        },
        onClose: () => {
          console.log('🔌 连接已关闭')
          this.updateConnectionStatus('disconnected')
          this.handleConnectionLoss()
        },
        onOpen: () => {
          console.log('🔓 连接已打开，等待服务器确认')
          // 等待服务器的connected消息确认
          this.updateConnectionStatus('connecting')
        },
        onToolMessage: (message) => {
          this.callbacks.onToolMessage?.(message)
        },
        onToolCallback: (message) => {
          this.callbacks.onToolCallback?.(message)
        },
        onTask: (message) => {
          this.callbacks.onTask?.(message)
        },
      })
      console.log(
        '✅ [ChatServiceManager.initialize] 初始化成功',
        this.connection == null,
      )
    } catch (error) {
      console.error('❌ [ChatServiceManager.doInitialize] 初始化失败:', error)
      // 恢复之前的回调函数
      this.callbacks = previousCallbacks
      throw error
    }

    this.getUserCity()
      .then((city) => {
        const greetingMessage = city
          ? `<user_online>你好，我来自${city}</user_online>`
          : '<user_online>你好</user_online>'
        console.log(
          '🔄 [ChatServiceManager.getUserCity] 发送问候消息:',
          greetingMessage,
        )
        return this.sendMessage(greetingMessage)
      })
      .catch((error) => {
        // 静默处理发送问候消息的错误，避免影响主要功能
        console.log(
          '🔄 [ChatServiceManager.getUserCity] 发送问候消息失败:',
          error.message,
        )
      })
  }

  // 获取用户城市信息的方法
  private getUserCity = async (): Promise<string> => {
    try {
      console.log('🔄 [ChatServiceManager.getUserCity] 获取用户城市信息')
      const response = await fetch('https://ipinfo.io/json')
      const data = await response.json()
      const city = data.city || ''

      // 如果成功获取到城市信息，缓存到cookie
      if (city) {
        document.cookie = `userCity=${encodeURIComponent(city)}; path=/; max-age=${7 * 24 * 60 * 60}` // 缓存7天
      }

      return city
    } catch (error) {
      console.error('Failed to get user city:', error)

      // 获取失败时，尝试从cookie中读取缓存的城市信息
      const cookies = document.cookie.split(';')
      const cityCookie = cookies.find((cookie) =>
        cookie.trim().startsWith('userCity='),
      )
      if (cityCookie) {
        const cachedCity = decodeURIComponent(cityCookie.split('=')[1])
        console.log('Using cached city from cookie:', cachedCity)
        return cachedCity
      }

      return ''
    }
  }

  // 更新连接状态
  private updateConnectionStatus(status: ConnectionStatus) {
    this.connectionStatus = status
    this.callbacks.onConnectionStatusChange?.(status)
  }

  // 处理LLM响应
  private handleLLMResponse(message: StreamMessage) {
    this.callbacks.onLLMResponse?.(message)
  }

  // 处理连接丢失 - 保持与原始逻辑一致
  private handleConnectionLoss() {
    if (
      !this.autoReconnect ||
      this.reconnectAttempts >= this.maxReconnectAttempts
    ) {
      console.log('🚫 不进行自动重连：', {
        autoReconnect: this.autoReconnect,
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
      })
      return
    }

    this.reconnectAttempts++
    // 保持原始的固定延迟策略：第1次3秒，第2次5秒
    const delay = this.reconnectAttempts === 1 ? 3000 : 5000

    console.log(
      `🔄 将在 ${delay}ms 后进行第 ${this.reconnectAttempts} 次重连尝试`,
    )

    const timeoutId = setTimeout(() => {
      this.attemptReconnection()
    }, delay)

    this.reconnectTimeouts.push(timeoutId)
  }

  // 尝试重连
  private attemptReconnection() {
    if (!this.autoReconnect) {
      return
    }

    console.log(`🔄 开始第 ${this.reconnectAttempts} 次重连尝试`)

    try {
      // 重新初始化连接，使用当前的回调函数
      this.initialize(this.callbacks)
    } catch (error) {
      console.error(`❌ 第 ${this.reconnectAttempts} 次重连失败:`, error)

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.handleConnectionLoss() // 继续尝试重连
      } else {
        console.error('❌ 已达到最大重连次数，停止重连')
      }
    }
  }

  // 清理重连定时器
  private clearReconnectTimeouts() {
    this.reconnectTimeouts.forEach((timeoutId) => clearTimeout(timeoutId))
    this.reconnectTimeouts = []
  }

  // 断开连接
  disconnect() {
    this.autoReconnect = false // 主动断开时禁用自动重连
    this.clearReconnectTimeouts()

    if (this.connection) {
      console.log('🔌 断开连接并清空连接对象')
      this.connection.close()
      this.connection = null // 清空连接对象
    }
    // 注释掉清空回调函数的代码，避免在用户ID变化时丢失新设置的回调
    // this.callbacks = {}
    this.updateConnectionStatus('disconnected')
  }

  // 检查心跳状态，如果需要则触发重连
  private async checkHeartbeatAndReconnectIfNeeded(): Promise<void> {
    const now = Date.now()
    const timeSinceLastHeartbeat = now - this.lastHeartbeatTime

    // 如果距离上次心跳超过3分钟，触发重连
    if (
      this.lastHeartbeatTime > 0 &&
      timeSinceLastHeartbeat > this.heartbeatTimeoutMs
    )  {
      console.log(
        `💓 [ChatServiceManager] 心跳超时，距离上次心跳: ${Math.round(timeSinceLastHeartbeat / 1000)}秒，触发重连`,
      )

      // 重置心跳时间，避免重复触发
      this.lastHeartbeatTime = 0

      // 触发透明重连
      await this.performTransparentReconnect()
    }

    if (!this.connection || !this.connection.isConnected()) {
      await this.performTransparentReconnect()
    }

  }

  // 执行透明重连
  private async performTransparentReconnect(): Promise<void> {
    console.log('🔄 [ChatServiceManager] 开始透明重连')

    try {
      // 保存当前回调函数
      const currentCallbacks = this.callbacks

      // 暂时禁用自动重连，避免在透明重连期间触发额外的重连
      const originalAutoReconnect = this.autoReconnect
      this.autoReconnect = false

      // 断开当前连接
      if (this.connection) {
        this.connection.close()
        this.connection = null
      }

      // 重置重连计数器，因为这是主动的透明重连
      this.reconnectAttempts = 0

      // 重新初始化连接
      this.initialize(currentCallbacks)

      // 恢复自动重连设置
      this.autoReconnect = originalAutoReconnect

      console.log('✅ [ChatServiceManager] 透明重连成功')
    } catch (error) {
      console.error('❌ [ChatServiceManager] 透明重连失败:', error)

      // 恢复自动重连设置
      this.autoReconnect = true

      // 通知错误，但不抛出异常，让正常的重连机制处理
      this.callbacks.onError?.(`心跳检测重连失败，将使用常规重连机制`)

      // 触发常规的连接丢失处理
      this.handleConnectionLoss()
    }
  }

  // 发送消息
  async sendMessage(content: string): Promise<void> {
    console.log('发送消息:', this.connection == null)

    // 发送消息前检查心跳状态
    await this.checkHeartbeatAndReconnectIfNeeded()

    if (!this.connection || !this.connection.isConnected()) {
      throw new Error('未连接到聊天服务')
    }

    // 发送到服务器
    const request: SendMessageRequest = {
      user_id: this.currentUserId,
      content,
      message_type: MessageType.TEXT,
      metadata: {
        source: 'mobile_chat',
        timestamp: new Date().toISOString(),
      },
    }

    try {
      await this.client.sendMessage(request)
    } catch (error) {
      console.error('发送消息失败:', error)
      this.callbacks.onError?.(`发送失败: ${error}`)
      throw error
    }
  }

  // 发送用户档案消息（静态方法，不依赖实例）
  static async sendProfileMessage(content: string): Promise<void> {
    console.log('发送用户档案消息:', content)

    // 获取认证信息
    const authState = useAuthStore.getState().auth
    if (!authState.isLoggedIn()) {
      throw new Error('用户未登录')
    }

    const userId =
      authState.userId || authState.user?.name || authState.user?.id
    const token = authState.token

    if (!userId) {
      throw new Error('无法获取用户ID')
    }

    if (!token) {
      throw new Error('无法获取认证token')
    }

    // 构建请求数据
    const request: SendMessageRequest = {
      user_id: userId,
      content,
      message_type: MessageType.PROFILE,
      // profile 消息不需要 metadata
    }

    // 构建完整的 API URL
    const baseURL = import.meta.env.VITE_API_URL
      ? `${import.meta.env.VITE_API_URL}/emotionmind`
      : `${GATEWAY_URL}/emotionmind`
    const apiUrl = `${baseURL}/api/v1/message/send`

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 检查响应格式是否符合预期
      if (!data.success && data.error) {
        throw new Error(data.error)
      }

      console.log('用户档案消息发送成功')
    } catch (error) {
      console.error('发送用户档案消息失败:', error)
      throw error
    }
  }

  // 重置服务（用于用户登出等场景）
  reset(): void {
    this.disconnect()
    this.currentUserId = ''
    this.reconnectAttempts = 0
    this.lastHeartbeatTime = 0 // 重置心跳时间
    // 在重置时清空回调函数
    this.callbacks = {}
  }

  // 获取连接状态
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus
  }

  // 获取心跳状态信息
  getHeartbeatStatus(): {
    lastHeartbeatTime: number
    timeSinceLastHeartbeat: number
    isHeartbeatHealthy: boolean
  } {
    const now = Date.now()
    const timeSinceLastHeartbeat =
      this.lastHeartbeatTime > 0 ? now - this.lastHeartbeatTime : -1
    const isHeartbeatHealthy =
      this.lastHeartbeatTime === 0 ||
      timeSinceLastHeartbeat < this.heartbeatTimeoutMs

    return {
      lastHeartbeatTime: this.lastHeartbeatTime,
      timeSinceLastHeartbeat,
      isHeartbeatHealthy,
    }
  }

  // 上传文件的公共方法
  async uploadFile(
    file: File,
    pathPrefix?: string,
  ): Promise<DirectUploadResponse> {
    return await this.client.uploadFile(file, pathPrefix)
  }

  // 添加一个获取客户端的方法（仅用于调试或特殊情况）
  getClient(): EmotionMindClient {
    return this.client
  }
}

// 导出类和单例实例
export { ChatServiceManager }
export const chatServiceManager = ChatServiceManager.getInstance()
