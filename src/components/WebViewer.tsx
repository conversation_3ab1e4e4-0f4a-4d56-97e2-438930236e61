import { memo, useEffect, useState, useRef } from 'react'
import { createPortal } from 'react-dom'
import { App as cap } from '@capacitor/app'
import { <PERSON>rows<PERSON> } from '@capacitor/browser'

type Props = {
  url: string
  title: string
  isOpen: boolean
  onClose: () => void
}

const WebViewer = ({ url, title, isOpen, onClose }: Props) => {
  const [isLoading, setIsLoading] = useState(true)
  const [isAnimating, setIsAnimating] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const loadTimeoutRef = useRef<NodeJS.Timeout>()
  const crossOriginCheckRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (isOpen) {
      // 重置状态
      setIsLoading(true)
      setHasError(false)
      // 开始显示组件
      setShouldRender(true)
      // 禁止背景滚动
      document.body.style.overflow = 'hidden'
      // 设置全局标志，禁用默认返回键处理
      window.shouldHandleBackButton = false

      // 设置加载超时检测，缩短超时时间
      loadTimeoutRef.current = setTimeout(() => {
        if (isLoading && !isRedirecting) {
          console.log('iframe加载超时，跳转到浏览器')
          handleCrossOriginDetected()
        }
      }, 5000) // 缩短超时时间到5秒

      // 短暂延迟后开始动画，让组件有时间渲染
      const timer = setTimeout(() => {
        setIsAnimating(true)
      }, 50)

      return () => {
        clearTimeout(timer)
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current)
        }
        if (crossOriginCheckRef.current) {
          clearTimeout(crossOriginCheckRef.current)
        }
      }
    } else {
      // 开始关闭动画
      setIsAnimating(false)
      // 恢复默认返回键处理
      window.shouldHandleBackButton = true
      // 清理超时定时器
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current)
      }
      if (crossOriginCheckRef.current) {
        clearTimeout(crossOriginCheckRef.current)
      }
      // 动画结束后隐藏组件
      const timer = setTimeout(() => {
        setShouldRender(false)
        // 恢复背景滚动
        document.body.style.overflow = ''
      }, 350)
      return () => clearTimeout(timer)
    }
  }, [isOpen, isLoading])

  // 添加移动端返回键监听器
  useEffect(() => {
    if (!isOpen) return

    let backButtonListener: any = null

    const setupListener = async () => {
      backButtonListener = await cap.addListener('backButton', () => {
        // 只有当 WebViewer 打开时才处理返回键
        if (window.shouldHandleBackButton === false) {
          onClose()
        }
      })
    }

    setupListener()

    return () => {
      if (backButtonListener) {
        backButtonListener.remove()
      }
    }
  }, [isOpen, onClose])

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      document.body.style.overflow = ''
      window.shouldHandleBackButton = true
    }
  }, [])

  const handleCrossOriginDetected = async () => {
    console.log('检测到跨域限制，正在跳转到浏览器...')
    setIsRedirecting(true)
    
    // 停止iframe加载
    if (iframeRef.current) {
      iframeRef.current.src = 'about:blank'
    }
    
    // 清理所有定时器
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current)
    }
    if (crossOriginCheckRef.current) {
      clearTimeout(crossOriginCheckRef.current)
    }
    
    try {
      await Browser.open({ url })
      onClose() // 关闭当前弹窗
    } catch (error) {
      console.error('打开浏览器失败:', error)
      // 降级到window.open
      window.open(url, '_blank')
      onClose()
    }
  }

  const handleIframeLoad = () => {
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current)
    }

    // 立即进行跨域检测，不等待1秒
    crossOriginCheckRef.current = setTimeout(() => {
      try {
        const iframe = iframeRef.current
        if (iframe && iframe.contentWindow) {
          // 尝试访问iframe内容，如果跨域会抛出异常
          const iframeDoc = iframe.contentWindow.document
          const body = iframeDoc.body

          // 检查是否有实际内容
          if (!body || body.innerHTML.trim() === '' ||
            body.textContent?.trim() === '' ||
            iframeDoc.title === '' ||
            body.innerHTML.includes('X-Frame-Options') ||
            body.innerHTML.includes('frame-ancestors')) {
            handleCrossOriginDetected()
            return
          }
        }
        // 如果没有问题，正常完成加载
        setIsLoading(false)
      } catch (error) {
        // 跨域访问被阻止，立即跳转到浏览器
        console.log('iframe存在跨域限制，立即跳转:', error)
        handleCrossOriginDetected()
      }
    }, 500) // 缩短检测时间到0.5秒
  }

  const handleIframeError = () => {
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current)
    }
    if (crossOriginCheckRef.current) {
      clearTimeout(crossOriginCheckRef.current)
    }
    // iframe加载错误，直接跳转到浏览器
    handleCrossOriginDetected()
  }

  const openInBrowser = async () => {
    try {
      await Browser.open({ url })
      onClose() // 关闭当前弹窗
    } catch (error) {
      console.error('打开浏览器失败:', error)
      // 降级到window.open
      window.open(url, '_blank')
      onClose()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleBackdropClick = () => {
    onClose()
  }

  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  if (!shouldRender) return null

  return createPortal(
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ease-out ${isAnimating
          ? 'bg-black bg-opacity-50 backdrop-blur-sm'
          : 'bg-black bg-opacity-0'
        }`}
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div
        className={`flex h-full max-h-full w-full max-w-6xl transform flex-col bg-white shadow-2xl transition-all duration-300 ease-out ${isAnimating
            ? 'translate-y-0 scale-100 opacity-100'
            : 'translate-y-4 scale-50 opacity-50'
          }`}
        onClick={handleContentClick}
        style={{
          transformOrigin: 'center center',
        }}
      >
        {/* 头部 */}
        <div className='relative flex items-center justify-between rounded-t-lg border-b border-gray-200 bg-white px-4 py-2'>
          {/* 加载进度条背景 */}
          {isLoading && (
            <div className="absolute inset-0 overflow-hidden rounded-t-lg">
              <div className="h-full w-full bg-gradient-to-r from-transparent via-blue-50 to-transparent animate-pulse"></div>
              <div
                className="absolute inset-0 bg-gradient-to-r from-blue-100 via-blue-200 to-blue-100 opacity-30"
                style={{
                  animation: 'loading-sweep 2s ease-in-out infinite',
                  backgroundSize: '200% 100%',
                }}
              ></div>
            </div>
          )}

          <div className='relative z-10 min-w-0 flex-1'>
            <div className='flex items-center space-x-3'>
              <div>
                <h2 className='truncate text-base font-medium text-gray-900'>
                  {title}
                </h2>
                <p className='truncate text-xs text-gray-500'>{url}</p>
              </div>

            </div>
          </div>
          <button
            onClick={onClose}
            className='relative z-10 ml-4 rounded-full p-1.5 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600'
            aria-label='关闭'
          >
            <svg
              className='h-5 w-5'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className='relative flex-1 overflow-hidden rounded-b-lg'>
          {isRedirecting ? (
            <div className='flex h-full items-center justify-center bg-gray-50'>
              <div className='text-center p-8'>
                <div className='mb-4 text-blue-500'>
                  <svg className='mx-auto h-16 w-16 animate-spin' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} 
                          d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15' />
                  </svg>
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>正在跳转到浏览器</h3>
                <p className='text-gray-600'>检测到跨域限制，正在为您打开系统浏览器...</p>
              </div>
            </div>
          ) : hasError ? (
            <div className='flex h-full items-center justify-center bg-gray-50'>
              <div className='text-center p-8'>
                <div className='mb-4 text-orange-500'>
                  <svg className='mx-auto h-16 w-16' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={1.5}
                      d='M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z' />
                  </svg>
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>页面无法在此显示</h3>
                <p className='text-gray-600 mb-6'>该网站可能有跨域限制，无法在iframe中正常显示</p>
                <div className='space-y-3'>
                  <button
                    onClick={openInBrowser}
                    className='w-full bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors font-medium'
                  >
                    在浏览器中打开
                  </button>
                  <button
                    onClick={onClose}
                    className='w-full bg-gray-200 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors'
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <iframe
              ref={iframeRef}
              src={url}
              className='h-full w-full border-0'
              title={title}
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              sandbox='allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation'
            />
          )}
        </div>
      </div>

      {/* CSS动画样式 */}
      <style>{`
        @keyframes loading-sweep {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </div>,
    document.body,
  )
}

export default memo(WebViewer)
