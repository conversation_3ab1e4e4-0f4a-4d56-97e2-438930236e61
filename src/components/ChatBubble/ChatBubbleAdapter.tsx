import { memo } from 'react'
import type { TConversationItem } from '@/stateV2/conversation'
import { EConversationType } from '@/stateV2/conversation'
import { MYSELF_ID } from '@/faker/wechat/user'
import { useParams } from 'react-router-dom'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { ModernChatBubble } from './index'

interface ChatBubbleAdapterProps {
  data: TConversationItem
  showAvatar?: boolean
}

const ChatBubbleAdapter = ({ data, showAvatar = true }: ChatBubbleAdapterProps) => {
  const { id } = useParams<{ id: string }>()
  const { type, role, upperText } = data
  
  // 确定发送者ID和头像
  const senderId = role === 'friend' ? id! : MYSELF_ID
  const avatarUrl = role === 'friend' ? '/assets/robot.png' : undefined

  // 渲染不同类型的消息内容
  const renderContent = () => {
    switch (type) {
      case EConversationType.text:
        // 将Slate格式转换为纯文本
        const markdownText = data.textContent
          ?.map((descendant) => {
            if (
              'type' in descendant &&
              descendant.type === 'paragraph' &&
              'children' in descendant
            ) {
              return descendant.children
                .map((child: any) => {
                  if ('text' in child) {
                    return child.text
                  }
                  return ''
                })
                .join('')
            } else if ('text' in descendant) {
              return descendant.text
            }
            return ''
          })
          .join('') || ''

        return (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              p: ({ children }) => <div className="mb-2 last:mb-0">{children}</div>,
              strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
              em: ({ children }) => <em className="italic">{children}</em>,
              code: ({ children, className }) => {
                const isInline = !className?.startsWith('language-')
                return isInline ? (
                  <code className="bg-black/10 px-1 py-0.5 rounded text-sm font-mono">
                    {children}
                  </code>
                ) : (
                  <pre className="bg-black/10 p-3 rounded-lg mt-2 overflow-x-auto">
                    <code className="text-sm font-mono">{children}</code>
                  </pre>
                )
              },
              a: ({ href, children }) => (
                <a 
                  href={href} 
                  className="text-blue-600 hover:text-blue-800 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {children}
                </a>
              ),
              ul: ({ children }) => <ul className="list-disc list-inside space-y-1 mt-2">{children}</ul>,
              ol: ({ children }) => <ol className="list-decimal list-inside space-y-1 mt-2">{children}</ol>,
              li: ({ children }) => <li className="text-sm">{children}</li>,
              blockquote: ({ children }) => (
                <blockquote className="border-l-4 border-gray-300 pl-4 italic mt-2 text-gray-600">
                  {children}
                </blockquote>
              ),
            }}
          >
            {markdownText}
          </ReactMarkdown>
        )

      case EConversationType.markdown:
        return (
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {data.markdownContent || ''}
          </ReactMarkdown>
        )

      case EConversationType.voice:
        return (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-sm">{data.duration || 0}"</span>
            {data.stt && (
              <div className="text-sm text-gray-600 mt-1">
                {data.stt}
              </div>
            )}
          </div>
        )

      case EConversationType.image:
        return (
          <div className="max-w-[200px]">
            <img 
              src={data.imageInfo} 
              alt="图片消息"
              className="rounded-lg max-w-full h-auto"
            />
          </div>
        )

      case EConversationType.centerText:
        return (
          <div className="text-center text-sm text-gray-600">
            {data.simpleContent}
          </div>
        )

      case EConversationType.notification:
        return (
          <div className="flex items-center space-x-2 text-sm">
            {data.icon && (
              <span className="text-red-500">⚠️</span>
            )}
            <span>{data.text}</span>
          </div>
        )

      default:
        return <div className="text-gray-500 text-sm">不支持的消息类型</div>
    }
  }

  // 系统消息和通知消息使用特殊样式
  if (type === EConversationType.centerText || type === EConversationType.notification) {
    return (
      <ModernChatBubble
        role="system"
        upperText={upperText}
        showAvatar={false}
      >
        {renderContent()}
      </ModernChatBubble>
    )
  }

  return (
    <ModernChatBubble
      role={role}
      avatar={avatarUrl}
      showAvatar={showAvatar}
      upperText={upperText}
    >
      {renderContent()}
    </ModernChatBubble>
  )
}

export default memo(ChatBubbleAdapter) 