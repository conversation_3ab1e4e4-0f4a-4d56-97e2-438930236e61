import { memo, type ReactNode } from 'react'
import { twMerge } from 'tailwind-merge'

interface ModernChatBubbleProps {
  children: ReactNode
  role: 'mine' | 'friend' | 'system'
  avatar?: string
  showAvatar?: boolean
  upperText?: string
  className?: string
}

const ModernChatBubble = ({
  children,
  role,
  avatar,
  showAvatar = true,
  upperText,
  className,
}: ModernChatBubbleProps) => {
  // 根据角色设置样式 - 完全匹配截图中的设计风格
  const getBubbleStyles = () => {
    switch (role) {
      case 'mine':
        return {
          containerClass: 'flex-row-reverse ml-auto',
          // 浅蓝紫色背景，完全匹配截图中的颜色
          bubbleClass: 'bg-[#C8D5FF] text-gray-900',
          maxWidth: 'max-w-[280px]'
        }
      case 'friend':
        return {
          containerClass: 'flex-row',
          // 纯白色背景，轻微阴影
          bubbleClass: 'bg-white text-gray-900 shadow-sm',
          maxWidth: 'max-w-[320px]'
        }
      case 'system':
        return {
          containerClass: 'flex-row justify-center',
          // 白色背景带阴影的错误提示
          bubbleClass: 'bg-white text-gray-800 shadow-lg border border-gray-100',
          maxWidth: 'max-w-[90%]'
        }
      default:
        return {
          containerClass: 'flex-row',
          bubbleClass: 'bg-white text-gray-900 shadow-sm',
          maxWidth: 'max-w-[320px]'
        }
    }
  }

  const styles = getBubbleStyles()

  // 系统消息（错误信息）的特殊样式
  if (role === 'system') {
    return (
      <div className="flex justify-center my-4 px-4">
        <div className={twMerge(
          'px-4 py-3 rounded-xl text-sm flex items-center space-x-3 w-full',
          'bg-white shadow-lg border border-gray-100',
          className
        )}>
          <div className="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
            <span className="text-red-500 text-sm">⚠</span>
          </div>
          <span className="text-gray-800 flex-1">{children}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="my-4 px-4">
      {/* 上方文本 */}
      {upperText && (
        <div className="text-xs text-gray-500 mb-2 text-center">
          {upperText}
        </div>
      )}
      
      {/* 消息容器 */}
      <div className={twMerge(
        'flex items-start space-x-3',
        styles.containerClass,
        role === 'mine' && 'space-x-reverse space-x-3'
      )}>
        {/* 头像 - 只有AI消息显示 */}
        {showAvatar && role === 'friend' && (
          <div className="flex-shrink-0 mt-1">
            {avatar ? (
              <img
                src={avatar}
                alt="avatar"
                className="w-8 h-8 rounded-md object-cover"
              />
            ) : (
              <div className="w-8 h-8 rounded-md bg-gray-600 flex items-center justify-center">
                <span className="text-white text-xs">🤖</span>
              </div>
            )}
          </div>
        )}

        {/* 气泡内容 */}
        <div className={twMerge('relative', styles.maxWidth)}>
          <div className={twMerge(
            'px-4 py-3 break-words',
            // 圆角设计 - 匹配截图
            'rounded-2xl',
            styles.bubbleClass,
            className
          )}>
            {/* 消息内容 */}
            <div className="leading-relaxed text-[15px]">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(ModernChatBubble) 