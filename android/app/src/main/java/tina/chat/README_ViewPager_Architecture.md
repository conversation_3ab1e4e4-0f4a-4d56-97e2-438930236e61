# ViewPager2 + SplashFragment 架构重构说明

## 概述

MainActivity 已经重构为使用 ViewPager2 + FragmentManager 的架构，支持启动页面、WebView 和网络诊断页面之间的无缝切换。这种架构提供了更好的用户体验和扩展性。

## 架构组件

### 1. MainActivity
- **作用**: 主控制器，管理 ViewPager2 和全局网络监控
- **主要功能**:
  - 初始化 ViewPager2 和适配器
  - 启动页面完成监听和自动切换
  - 全局网络状态监控和自动页面切换
  - 推送服务管理
  - 提供页面切换的公共方法

### 2. MainPagerAdapter
- **作用**: ViewPager2 的适配器，管理所有页面 Fragment
- **当前页面**:
  - `PAGE_SPLASH (0)`: 启动页面
  - `PAGE_WEBVIEW (1)`: WebView 页面
  - `PAGE_NETWORK_DIAGNOSTIC (2)`: 网络诊断页面

### 3. SplashFragment
- **作用**: 启动页面，基于 `src/pages/splash/index.tsx` 的设计
- **功能**:
  - 渐变背景和 Logo 显示
  - 分块式进度条动画
  - 模拟加载步骤（初始化、加载配置、连接服务器等）
  - 加载完成后自动切换到 WebView
  - 版权信息和版本号显示

### 4. WebViewFragment
- **作用**: 包装 Capacitor 的 WebView，处理 JavaScript 接口注入
- **功能**:
  - 从 MainActivity 获取 Bridge 和 WebView
  - 自动注入权限管理和 FunASR 接口
  - 提供 WebView 刷新功能
  - 处理 WebView 的生命周期管理

### 5. NetworkDiagnosticFragment
- **作用**: 网络诊断页面，原 NetworkDiagnosticView 的 Fragment 版本
- **功能**:
  - 实时网络状态检测和显示
  - 网络恢复通知
  - 重试功能

## 使用方法

### 启动流程
1. 应用启动时显示 `SplashFragment`
2. 启动页面执行加载动画（约 3.5 秒）
3. 加载完成后自动切换到 `WebViewFragment`
4. 如果网络异常，自动切换到 `NetworkDiagnosticFragment`

### 页面切换
```java
// 切换到启动页面
mainActivity.switchToSplash();

// 切换到 WebView 页面
mainActivity.switchToWebView();

// 切换到网络诊断页面
mainActivity.switchToNetworkDiagnostic();

// 获取当前页面
int currentPage = mainActivity.getCurrentPage();
```

### 自动网络监控
系统会自动监控网络状态：
- 网络异常时自动切换到网络诊断页面（启动页面期间除外）
- 网络恢复时自动刷新 WebView 并切换回主页面

### JavaScript 接口
WebView 中的 JavaScript 接口保持不变：
- `AndroidPermissions`: 权限管理
- `AndroidFunASR`: 语音识别

### 启动页面特性
- 基于 `src/pages/splash/index.tsx` 的设计风格
- 渐变背景效果
- Logo 和副标题显示
- 5 段式进度条动画
- 加载步骤文字提示
- 版权信息和版本号

## 如何添加新页面

### 1. 创建新的 Fragment
```java
public class YourNewFragment extends Fragment {
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // 创建你的 UI
        return yourView;
    }
}
```

### 2. 更新 MainPagerAdapter
```java
// 添加页面常量
public static final int PAGE_YOUR_NEW = 3;

// 更新页面总数
private static final int PAGE_COUNT = 4;

// 添加 Fragment 实例变量
private YourNewFragment yourNewFragment;

// 在 createFragment 中添加新页面
case PAGE_YOUR_NEW:
    if (yourNewFragment == null) {
        yourNewFragment = new YourNewFragment();
    }
    return yourNewFragment;

// 添加 getter 方法
public YourNewFragment getYourNewFragment() {
    return yourNewFragment;
}
```

### 3. 在 MainActivity 中添加切换方法
```java
public void switchToYourNew() {
    if (viewPager != null) {
        viewPager.setCurrentItem(MainPagerAdapter.PAGE_YOUR_NEW, true);
    }
}
```

## 网络监控机制

### 全局网络监控
- MainActivity 维护一个全局的 NetworkDiagnosticManager
- 自动检测网络状态变化
- 根据网络状态自动切换页面

### Fragment 级网络监控
- NetworkDiagnosticFragment 有自己的网络管理器
- 提供详细的网络诊断信息
- 支持手动重试

## 生命周期管理

### MainActivity
- `onCreate`: 初始化 ViewPager2、推送服务、网络监控
- `onResume`: 开始全局网络监控
- `onPause`: 暂停全局网络监控
- `onDestroy`: 清理所有资源

### Fragment
- 各 Fragment 管理自己的资源
- WebViewFragment 负责清理 FunASR 资源
- NetworkDiagnosticFragment 负责清理网络监控资源

## 优势

1. **模块化**: 每个页面都是独立的 Fragment，便于维护
2. **扩展性**: 轻松添加新的原生页面
3. **性能**: Fragment 的懒加载和资源管理
4. **用户体验**: 平滑的页面切换动画
5. **网络智能**: 自动网络状态检测和页面切换

## 注意事项

1. **ViewPager2 设置**: 已禁用用户滑动，只允许程序控制切换
2. **Fragment 实例**: 适配器会缓存 Fragment 实例，避免重复创建
3. **网络监控**: 全局和 Fragment 级别的双重网络监控
4. **资源清理**: 确保在适当的生命周期方法中清理资源

## 依赖项

已添加到 `build.gradle`:
```gradle
implementation "androidx.viewpager2:viewpager2:1.0.0"
```

## 迁移说明

原有的功能保持不变：
- JavaScript 接口正常工作
- 推送服务正常工作
- 权限管理正常工作
- 网络诊断功能增强

新增功能：
- 启动页面：基于 Web 版设计的原生启动页面
- 支持多页面切换：启动页面 → WebView → 网络诊断
- 自动网络状态管理：智能切换页面
- 更好的代码组织结构：模块化 Fragment 设计
- 易于扩展新功能：简单添加新的原生页面

## 启动流程变化

**之前**：直接显示 WebView

**现在**：
1. 显示启动页面（3.5秒加载动画）
2. 自动切换到 WebView
3. 根据网络状态智能切换页面

## 故障排除

### WebView 显示白屏
1. 检查 `WebViewFragment` 是否正确获取到 `Bridge`
2. 确认 `WebView` 是否正确添加到容器中
3. 查看日志中的 `WebViewFragment` 相关信息

### 启动页面不自动切换
1. 检查 `SplashFragment` 的 `OnSplashCompleteListener` 是否正确设置
2. 确认 `MainActivity.setupSplashCompleteListener()` 是否被调用
3. 查看日志中的 "Splash complete" 相关信息