package tina.chat;

import android.animation.ObjectAnimator;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * 启动页面 Fragment - 基于 src/pages/splash/index.tsx 的设计
 * 在应用启动时显示，WebView 加载完成后自动隐藏
 */
public class SplashFragment extends Fragment {
    
    private LinearLayout progressContainer;
    private TextView loadingText;
    private int currentProgress = 0;
    private Handler handler = new Handler();
    private OnSplashCompleteListener splashCompleteListener;
    
    public interface OnSplashCompleteListener {
        void onSplashComplete();
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 创建根布局 - 渐变背景
        LinearLayout rootLayout = new LinearLayout(getContext());
        rootLayout.setOrientation(LinearLayout.VERTICAL);
        rootLayout.setGravity(Gravity.CENTER);
        
        // 设置渐变背景 - 基于 splash 页面的设计
        GradientDrawable gradientBackground = new GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            new int[]{
                Color.parseColor("#4FFFFFFF"), // rgba(255, 255, 255, 0.31)
                Color.parseColor("#96B6C2CA"), // rgba(182, 194, 202, 0.59) 
                Color.parseColor("#A3B5C1C9")  // rgba(181, 193, 201, 0.64)
            }
        );
        rootLayout.setBackground(gradientBackground);
        
        // 上半部分 - Logo 和文字区域
        LinearLayout topSection = createTopSection();
        
        // 下半部分 - 进度条区域
        LinearLayout bottomSection = createBottomSection();
        
        // 底部版权信息
        LinearLayout copyrightSection = createCopyrightSection();
        
        // 设置权重分布
        LinearLayout.LayoutParams topParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, 0, 1.0f
        );
        topSection.setLayoutParams(topParams);
        
        LinearLayout.LayoutParams bottomParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, 0, 1.0f
        );
        bottomSection.setLayoutParams(bottomParams);
        
        rootLayout.addView(topSection);
        rootLayout.addView(bottomSection);
        rootLayout.addView(copyrightSection);
        
        return rootLayout;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 开始加载动画
        startLoadingAnimation();
    }
    
    private LinearLayout createTopSection() {
        LinearLayout topSection = new LinearLayout(getContext());
        topSection.setOrientation(LinearLayout.VERTICAL);
        topSection.setGravity(Gravity.CENTER);
        
        int padding = dpToPx(16);
        topSection.setPadding(padding, dpToPx(64), padding, dpToPx(40));
        
        // Logo 容器 - 矩形背景
        LinearLayout logoContainer = new LinearLayout(getContext());
        logoContainer.setGravity(Gravity.CENTER);
        
        GradientDrawable logoBackground = new GradientDrawable();
        logoBackground.setShape(GradientDrawable.RECTANGLE);
        logoBackground.setCornerRadius(dpToPx(60)); // 圆角矩形
        logoBackground.setColor(Color.parseColor("#F1E9DD")); // rgba(241, 233, 221, 1)
        logoContainer.setBackground(logoBackground);
        
        // 矩形容器尺寸
        int logoContainerWidth = dpToPx(180);
        int logoContainerHeight = dpToPx(140);
        LinearLayout.LayoutParams logoContainerParams = new LinearLayout.LayoutParams(logoContainerWidth, logoContainerHeight);
        logoContainerParams.bottomMargin = dpToPx(24);
        logoContainer.setLayoutParams(logoContainerParams);
        
        // Logo 图片
        ImageView logoImage = new ImageView(getContext());
        logoImage.setImageResource(R.drawable.logo);
        logoImage.setScaleType(ImageView.ScaleType.FIT_CENTER);
        
        // 设置logo图片大小，在矩形容器内显示更大
        int logoImageWidth = dpToPx(140);
        int logoImageHeight = dpToPx(120);
        LinearLayout.LayoutParams logoImageParams = new LinearLayout.LayoutParams(logoImageWidth, logoImageHeight);
        logoImage.setLayoutParams(logoImageParams);
        
        logoContainer.addView(logoImage);
        
        // 副标题图片 (splash_text.png)
        ImageView subtitleImage = new ImageView(getContext());
        subtitleImage.setImageResource(R.drawable.splash_text);
        subtitleImage.setScaleType(ImageView.ScaleType.FIT_CENTER);
        
        // 设置副标题图片大小 - 调大
        int subtitleWidth = dpToPx(280);
        int subtitleHeight = dpToPx(50);
        LinearLayout.LayoutParams subtitleParams = new LinearLayout.LayoutParams(subtitleWidth, subtitleHeight);
        subtitleImage.setLayoutParams(subtitleParams);
        
        topSection.addView(logoContainer);
        topSection.addView(subtitleImage);
        
        return topSection;
    }
    
    private LinearLayout createBottomSection() {
        LinearLayout bottomSection = new LinearLayout(getContext());
        bottomSection.setOrientation(LinearLayout.VERTICAL);
        bottomSection.setGravity(Gravity.CENTER);
        
        // 分块式进度条容器
        progressContainer = new LinearLayout(getContext());
        progressContainer.setOrientation(LinearLayout.HORIZONTAL);
        progressContainer.setGravity(Gravity.CENTER);
        
        // 创建5个进度块
        for (int i = 0; i < 5; i++) {
            View progressBlock = new View(getContext());
            
            GradientDrawable blockBackground = new GradientDrawable();
            blockBackground.setShape(GradientDrawable.RECTANGLE);
            blockBackground.setCornerRadius(dpToPx(8));
            blockBackground.setColor(Color.parseColor("#4DFFFFFF")); // rgba(255, 255, 255, 0.3)
            progressBlock.setBackground(blockBackground);
            
            LinearLayout.LayoutParams blockParams = new LinearLayout.LayoutParams(
                dpToPx(28), dpToPx(16)
            );
            if (i < 4) {
                blockParams.rightMargin = dpToPx(12);
            }
            blockParams.bottomMargin = dpToPx(4);
            blockParams.topMargin = dpToPx(4);

            progressBlock.setLayoutParams(blockParams);
            
            progressContainer.addView(progressBlock);
        }
        
        LinearLayout.LayoutParams progressParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        progressParams.bottomMargin = dpToPx(24);
        progressContainer.setLayoutParams(progressParams);
        
        // 加载文字
        loadingText = new TextView(getContext());
        loadingText.setText("正在初始化...");
        loadingText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        loadingText.setTextColor(Color.parseColor("#6c5544"));
        loadingText.setGravity(Gravity.CENTER);
        loadingText.setAlpha(0.8f);
        loadingText.setVisibility(View.INVISIBLE);
        
        bottomSection.addView(progressContainer);
        bottomSection.addView(loadingText);
        
        return bottomSection;
    }
    
    private LinearLayout createCopyrightSection() {
        LinearLayout copyrightSection = new LinearLayout(getContext());
        copyrightSection.setOrientation(LinearLayout.VERTICAL);
        copyrightSection.setGravity(Gravity.CENTER);
        
        LinearLayout.LayoutParams copyrightParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        copyrightParams.bottomMargin = dpToPx(32);
        copyrightSection.setLayoutParams(copyrightParams);
        
        // 版权信息
        TextView copyrightText = new TextView(getContext());
        copyrightText.setText("© 2025 Tina Chat. All rights reserved.");
        copyrightText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        copyrightText.setTextColor(Color.WHITE);
        copyrightText.setGravity(Gravity.CENTER);
        copyrightText.setAlpha(0.75f);
        
        // 版本号
        TextView versionText = new TextView(getContext());
        versionText.setText("版本号：1.3.13");
        versionText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        versionText.setTextColor(Color.WHITE);
        versionText.setGravity(Gravity.CENTER);
        versionText.setAlpha(0.75f);
        
        LinearLayout.LayoutParams versionParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        versionParams.topMargin = dpToPx(4);
        versionText.setLayoutParams(versionParams);
        
        copyrightSection.addView(copyrightText);
        copyrightSection.addView(versionText);
        
        return copyrightSection;
    }
    
    private void startLoadingAnimation() {
        // 模拟加载步骤
        String[] loadingSteps = {
            ".",
            "..",
            "...",
            "....",
            "....."
        };
        
        Runnable updateProgress = new Runnable() {
            @Override
            public void run() {
                if (currentProgress < 5) {
                    // 更新进度块
                    updateProgressBlock(currentProgress);
                    
                    // 更新加载文字
                    if (currentProgress < loadingSteps.length) {
                        loadingText.setText(loadingSteps[currentProgress]);
                    }
                    
                    currentProgress++;
                    handler.postDelayed(this, 200); // 每200ms更新一次
                }
                // 移除自动完成逻辑，现在由 WebView 准备就绪时触发
            }
        };
        
        handler.postDelayed(updateProgress, 200); // 延迟200ms开始
    }
    
    /**
     * 手动完成启动页面
     * 由外部调用（通常是 WebView 准备就绪时）
     */
    public void completeSplash() {
        if (splashCompleteListener != null) {
            handler.post(() -> {
                loadingText.setText("启动完成");
                splashCompleteListener.onSplashComplete();
            });
        }
    }
    
    private void updateProgressBlock(int index) {
        if (index < progressContainer.getChildCount()) {
            View progressBlock = progressContainer.getChildAt(index);
            
            // 创建激活状态的背景
            GradientDrawable activeBackground = new GradientDrawable();
            activeBackground.setShape(GradientDrawable.RECTANGLE);
            activeBackground.setCornerRadius(dpToPx(8));
            activeBackground.setColor(Color.parseColor("#EFECE3")); // rgba(239,236,227,1)
            
            // 添加阴影效果
            progressBlock.setBackground(activeBackground);
            progressBlock.setElevation(dpToPx(4));
            
            // 添加动画效果
            ObjectAnimator scaleX = ObjectAnimator.ofFloat(progressBlock, "scaleX", 0.8f, 1.0f);
            ObjectAnimator scaleY = ObjectAnimator.ofFloat(progressBlock, "scaleY", 0.8f, 1.0f);
            scaleX.setDuration(300);
            scaleY.setDuration(300);
            scaleX.start();
            scaleY.start();
        }
    }
    
    /**
     * 设置启动完成监听器
     */
    public void setOnSplashCompleteListener(OnSplashCompleteListener listener) {
        this.splashCompleteListener = listener;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
    }
    
    private int dpToPx(int dp) {
        return (int) TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP, 
            dp, 
            getContext().getResources().getDisplayMetrics()
        );
    }
}