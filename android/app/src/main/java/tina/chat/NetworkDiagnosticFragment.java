package tina.chat;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * 网络诊断 Fragment
 */
public class NetworkDiagnosticFragment extends Fragment {
    
    private NetworkDiagnosticView diagnosticView;
    private NetworkDiagnosticManager networkManager;
    private OnNetworkRestoredListener networkRestoredListener;
    
    public interface OnNetworkRestoredListener {
        void onNetworkRestored();
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d("NetworkDiagnosticFragment", "onCreateView called");
        
        // 创建网络诊断视图
        diagnosticView = new NetworkDiagnosticView(getContext());
        
        // 设置重试按钮点击监听
        diagnosticView.setOnRetryClickListener(() -> {
            if (networkManager != null) {
                networkManager.startMonitoring();
            }
        });
        
        // 确保视图可见
        diagnosticView.setVisibility(View.VISIBLE);
        diagnosticView.setAlpha(1.0f);
        
        return diagnosticView;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化网络检测管理器
        initNetworkManager();
        
        // 显示诊断界面
        if (diagnosticView != null) {
            diagnosticView.showWithAnimation();
        }
    }
    
    private void initNetworkManager() {
        if (getContext() == null) return;
        
        networkManager = new NetworkDiagnosticManager(getContext());
        
        // 设置网络状态监听
        networkManager.setNetworkStatusListener(new NetworkDiagnosticManager.NetworkStatusListener() {
            @Override
            public void onNetworkStatusChanged(NetworkDiagnosticManager.NetworkStatus status) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        if (diagnosticView != null) {
                            diagnosticView.updateNetworkStatus(status);
                        }
                        
                        // 如果网络完全正常，通知主Activity
                        if (status.isFullyConnected() && networkRestoredListener != null) {
                            networkRestoredListener.onNetworkRestored();
                        }
                    });
                }
            }
            
            @Override
            public void onNetworkRestored() {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        if (diagnosticView != null) {
                            // 显示网络恢复消息
                            diagnosticView.showNetworkRestored();
                        }
                        
                        // 通知主Activity网络已恢复
                        if (networkRestoredListener != null) {
                            networkRestoredListener.onNetworkRestored();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // 开始网络监控
        if (networkManager != null) {
            networkManager.startMonitoring();
        }
        
        // 确保诊断界面可见
        if (diagnosticView != null && diagnosticView.getVisibility() != View.VISIBLE) {
            diagnosticView.showWithAnimation();
        }
    }
    
    @Override
    public void onPause() {
        super.onPause();
        // 暂停网络监控以节省资源
        if (networkManager != null) {
            networkManager.stopMonitoring();
        }
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 清理网络检测资源
        if (networkManager != null) {
            networkManager.cleanup();
        }
    }
    
    /**
     * 设置网络恢复监听器
     */
    public void setOnNetworkRestoredListener(OnNetworkRestoredListener listener) {
        this.networkRestoredListener = listener;
    }
    
    /**
     * 获取当前网络状态
     */
    public NetworkDiagnosticManager.NetworkStatus getCurrentNetworkStatus() {
        return networkManager != null ? networkManager.getCurrentStatus() : null;
    }
    
    /**
     * 检查网络是否已连接
     */
    public boolean isNetworkConnected() {
        return networkManager != null && networkManager.isConnected();
    }
}