package tina.chat;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.util.TypedValue;
import android.view.Gravity;
import android.widget.LinearLayout;
import android.widget.TextView;

public class NetworkStatusItem extends LinearLayout {
    private TextView iconView;
    private TextView labelView;
    private TextView statusView;
    private TextView statusDot;
    
    public enum Status {
        CHECKING("检查中", "#6C6C70", "●"), // 统一灰色
        SUCCESS("正常", "#6C6C70", "●"),      // 统一灰色
        FAILED("失败", "#FF3B30", "●"),       // 只有失败显示红色
        DISABLED("未连接", "#C7C7CC", "○");   // 浅灰色
        
        private final String text;
        private final String color;
        private final String dot;
        
        Status(String text, String color, String dot) {
            this.text = text;
            this.color = color;
            this.dot = dot;
        }
        
        public String getText() { return text; }
        public String getColor() { return color; }
        public String getDot() { return dot; }
    }
    
    public NetworkStatusItem(Context context, String icon, String label) {
        super(context);
        initView(icon, label);
    }
    
    private void initView(String icon, String label) {
        setOrientation(HORIZONTAL);
        setGravity(Gravity.CENTER_VERTICAL);
        
        int padding = dpToPx(16);
        setPadding(padding, dpToPx(12), padding, dpToPx(12));
        
        createIconView(icon);
        createLabelView(label);
        createStatusView();
        
        // 设置初始状态为检查中
        setStatus(Status.CHECKING);
    }
    
    private void createIconView(String icon) {
        // 创建图标容器
        LinearLayout iconContainer = new LinearLayout(getContext());
        iconContainer.setOrientation(HORIZONTAL);
        iconContainer.setGravity(Gravity.CENTER);
        
        // 创建圆形背景的图标
        iconView = new TextView(getContext());
        iconView.setText(getMonochromeIcon(icon));
        iconView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        iconView.setTextColor(Color.parseColor("#6C6C70"));
        iconView.setGravity(Gravity.CENTER);
        
        // 创建圆形背景
        GradientDrawable background = new GradientDrawable();
        background.setShape(GradientDrawable.OVAL);
        background.setColor(Color.parseColor("#F2F2F7"));
        background.setStroke(dpToPx(1), Color.parseColor("#E5E5EA"));
        
        int iconSize = dpToPx(36);
        iconView.setLayoutParams(new LinearLayout.LayoutParams(iconSize, iconSize));
        iconView.setBackground(background);
        
        // 创建状态点
        statusDot = new TextView(getContext());
        statusDot.setText("●");
        statusDot.setTextSize(TypedValue.COMPLEX_UNIT_SP, 8);
        statusDot.setTextColor(Color.parseColor("#FF9500"));
        
        LinearLayout.LayoutParams dotParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        dotParams.leftMargin = -dpToPx(8);
        dotParams.topMargin = -dpToPx(8);
        statusDot.setLayoutParams(dotParams);
        
        iconContainer.addView(iconView);
        iconContainer.addView(statusDot);
        
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
            dpToPx(44),
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        containerParams.rightMargin = dpToPx(16);
        iconContainer.setLayoutParams(containerParams);
        
        addView(iconContainer);
    }
    
    private String getMonochromeIcon(String originalIcon) {
        // 使用简洁的单色图标字符
        switch (originalIcon) {
            case "📱": return "●";  // 手机信号 - 使用实心圆
            case "📶": return "▲";  // 网络连接 - 使用三角形
            case "🌐": return "◐";  // 互联网 - 使用半圆
            case "🖥️": return "■";  // 服务器 - 使用方形
            default: return "●";
        }
    }
    
    private void createLabelView(String label) {
        labelView = new TextView(getContext());
        labelView.setText(label);
        labelView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        labelView.setTextColor(Color.parseColor("#6C6C70")); // 使用统一的灰色
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            0,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            1.0f
        );
        labelView.setLayoutParams(params);
        
        addView(labelView);
    }
    
    private void createStatusView() {
        statusView = new TextView(getContext());
        statusView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        statusView.setGravity(Gravity.END);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        statusView.setLayoutParams(params);
        
        addView(statusView);
    }
    
    public void setStatus(Status status) {
        statusView.setText(status.getText());
        statusView.setTextColor(Color.parseColor(status.getColor()));
        
        // 更新状态点
        statusDot.setText(status.getDot());
        statusDot.setTextColor(Color.parseColor(status.getColor()));
        
        // 添加简单的动画效果
        statusView.animate()
            .alpha(0.7f)
            .setDuration(150)
            .withEndAction(() -> {
                statusView.animate()
                    .alpha(1.0f)
                    .setDuration(150)
                    .start();
            })
            .start();
            
        // 状态点动画
        statusDot.animate()
            .scaleX(1.2f)
            .scaleY(1.2f)
            .setDuration(200)
            .withEndAction(() -> {
                statusDot.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(200)
                    .start();
            })
            .start();
    }
    
    public void updateLabel(String newLabel) {
        labelView.setText(newLabel);
    }
    
    private int dpToPx(int dp) {
        return (int) TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            getContext().getResources().getDisplayMetrics()
        );
    }
}