package tina.chat;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.getcapacitor.Bridge;

/**
 * WebView Fragment - 包装 Capacitor 的 WebView
 */
public class WebViewFragment extends Fragment {
    
    // 移除 FunASRManager 相关成员
    // private FunASRManager funASRManager;
    private Bridge bridge;
    private WebView webView;
    private OnWebViewReadyListener webViewReadyListener;
    
    public interface OnWebViewReadyListener {
        void onWebViewReady();
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 创建容器
        FrameLayout frameLayout = new FrameLayout(getContext());
        frameLayout.setLayoutParams(new FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ));
        
        return frameLayout;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        Log.d("WebViewFragment", "onViewCreated called");
        
        // 延迟初始化 WebView，确保 MainActivity 的 Bridge 已经准备好
        view.post(() -> {
            initializeWebView(view);
        });
    }
    
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && webView == null && getView() != null) {
            // 当 Fragment 变为可见且 WebView 还未初始化时，尝试初始化
            initializeWebView(getView());
        }
    }
    
    private void initializeWebView(View containerView) {
        if (webView != null) {
            Log.d("WebViewFragment", "WebView already initialized");
            return;
        }
        
        // 从 MainActivity 获取 Bridge 和 WebView
        if (getActivity() instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) getActivity();
            bridge = mainActivity.getBridge();
            
            if (bridge != null) {
                webView = bridge.getWebView();
                
                // 将 WebView 添加到 Fragment 的容器中
                if (webView != null) {
                    Log.d("WebViewFragment", "Found WebView, adding to container");
                    
                    // 如果 WebView 已经有父容器，先移除
                    if (webView.getParent() != null) {
                        Log.d("WebViewFragment", "Removing WebView from previous parent");
                        ((ViewGroup) webView.getParent()).removeView(webView);
                    }
                    
                    FrameLayout container = (FrameLayout) containerView;
                    container.removeAllViews(); // 清除容器中的所有视图
                    container.addView(webView, new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    ));
                    
                    // 确保 WebView 可见
                    webView.setVisibility(View.VISIBLE);
                    container.setVisibility(View.VISIBLE);
                    
                    // 初始化接口
                    initializeInterfaces();
                    
                    Log.d("WebViewFragment", "WebView initialized successfully");
                    
                    // 延迟通知 WebView 已准备就绪，确保页面完全加载
                    webView.postDelayed(() -> {
                        if (webViewReadyListener != null) {
                            webViewReadyListener.onWebViewReady();
                        }
                    }, 10);
                } else {
                    Log.w("WebViewFragment", "WebView is null, retrying in 200ms");
                    // 如果 WebView 还没准备好，延迟重试
                    containerView.postDelayed(() -> initializeWebView(containerView), 200);
                }
            } else {
                Log.w("WebViewFragment", "Bridge is null, retrying in 200ms");
                // 如果 Bridge 还没准备好，延迟重试
                containerView.postDelayed(() -> initializeWebView(containerView), 200);
            }
        } else {
            Log.e("WebViewFragment", "Activity is not MainActivity");
        }
    }
    
    private void initializeInterfaces() {
        // 不再在 Fragment 注入 JS 接口，全部交由 MainActivity 负责
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 不再清理 FunASRManager 资源，由 MainActivity 负责
    }
    
    /**
     * 刷新WebView
     */
    public void refreshWebView() {
        if (webView != null) {
            webView.post(() -> {
                webView.reload();
            });
        }
    }
    
    /**
     * 获取WebView实例
     */
    public WebView getWebView() {
        return webView;
    }
    
    /**
     * 设置 WebView 准备就绪监听器
     */
    public void setOnWebViewReadyListener(OnWebViewReadyListener listener) {
        this.webViewReadyListener = listener;
    }
}