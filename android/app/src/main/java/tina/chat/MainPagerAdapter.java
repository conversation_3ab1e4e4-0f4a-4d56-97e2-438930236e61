package tina.chat;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

/**
 * 主界面 ViewPager2 适配器
 * 管理 WebView 和网络诊断等页面的切换
 */
public class MainPagerAdapter extends FragmentStateAdapter {
    
    // 页面索引常量
    public static final int PAGE_SPLASH = 0;
    public static final int PAGE_WEBVIEW = 1;
    public static final int PAGE_NETWORK_DIAGNOSTIC = 2;
    
    // 页面总数
    private static final int PAGE_COUNT = 3;
    
    private SplashFragment splashFragment;
    private WebViewFragment webViewFragment;
    private NetworkDiagnosticFragment networkDiagnosticFragment;
    
    public MainPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }
    
    @NonNull
    @Override
    public Fragment createFragment(int position) {
        android.util.Log.d("MainPagerAdapter", "Creating fragment for position: " + position);
        
        switch (position) {
            case PAGE_SPLASH:
                if (splashFragment == null) {
                    splashFragment = new SplashFragment();
                    android.util.Log.d("MainPagerAdapter", "Created new SplashFragment");
                }
                return splashFragment;
                
            case PAGE_WEBVIEW:
                if (webViewFragment == null) {
                    webViewFragment = new WebViewFragment();
                    android.util.Log.d("MainPagerAdapter", "Created new WebViewFragment");
                }
                return webViewFragment;
                
            case PAGE_NETWORK_DIAGNOSTIC:
                if (networkDiagnosticFragment == null) {
                    networkDiagnosticFragment = new NetworkDiagnosticFragment();
                    android.util.Log.d("MainPagerAdapter", "Created new NetworkDiagnosticFragment");
                } else {
                    android.util.Log.d("MainPagerAdapter", "Reusing existing NetworkDiagnosticFragment");
                }
                return networkDiagnosticFragment;
                
            default:
                throw new IllegalArgumentException("Invalid page position: " + position);
        }
    }
    
    @Override
    public int getItemCount() {
        return PAGE_COUNT;
    }
    
    /**
     * 获取启动页 Fragment 实例
     */
    public SplashFragment getSplashFragment() {
        return splashFragment;
    }
    
    /**
     * 获取 WebView Fragment 实例
     */
    public WebViewFragment getWebViewFragment() {
        return webViewFragment;
    }
    
    /**
     * 获取网络诊断 Fragment 实例
     */
    public NetworkDiagnosticFragment getNetworkDiagnosticFragment() {
        return networkDiagnosticFragment;
    }
    
    /**
     * 根据位置获取页面标题（可选，用于调试）
     */
    public String getPageTitle(int position) {
        switch (position) {
            case PAGE_SPLASH:
                return "Splash";
            case PAGE_WEBVIEW:
                return "WebView";
            case PAGE_NETWORK_DIAGNOSTIC:
                return "Network Diagnostic";
            default:
                return "Unknown";
        }
    }
}