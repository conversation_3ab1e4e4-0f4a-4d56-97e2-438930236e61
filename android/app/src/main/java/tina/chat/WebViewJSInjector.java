package tina.chat;

import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import java.util.List;

public class WebViewJSInjector {
    private static FunASRManager funASRManager;
    private static PermissionInterface permissionInterface;
    private static MainActivity mainActivity;
    private static WebView webView;

    public static void init(MainActivity activity, WebView wv) {
        mainActivity = activity;
        webView = wv;
        // 注入权限管理
        permissionInterface = new PermissionInterface();
        webView.addJavascriptInterface(permissionInterface, "AndroidPermissions");
        // 注入 ASR
        funASRManager = new FunASRManager(activity, webView);
        webView.addJavascriptInterface(funASRManager, "AndroidFunASR");
    }

    /**
     * 权限 JS 接口
     */
    public static class PermissionInterface {
        @JavascriptInterface
        public void checkMicrophonePermission(String callbackName) {
            mainActivity.runOnUiThread(() -> {
                boolean hasPermission = XXPermissions.isGranted(mainActivity, Permission.RECORD_AUDIO);
                String result = String.format(
                        "{ \"hasPermission\": %s, \"permission\": \"microphone\" }",
                        hasPermission);
                executeCallback(callbackName, result);
            });
        }

        @JavascriptInterface
        public void requestMicrophonePermission(String callbackName) {
            mainActivity.runOnUiThread(() -> {
                XXPermissions.with(mainActivity)
                        .permission(Permission.RECORD_AUDIO)
                        .request(new OnPermissionCallback() {
                            @Override
                            public void onGranted(List<String> permissions, boolean allGranted) {
                                String result = String.format(
                                        "{ \"granted\": true, \"permission\": \"microphone\", \"message\": \"麦克风权限已授权\" }");
                                executeCallback(callbackName, result);
                            }

                            @Override
                            public void onDenied(List<String> permissions, boolean doNotAskAgain) {
                                String message = doNotAskAgain ?
                                        "麦克风权限已被永久拒绝，请前往设置页面手动开启" : "麦克风权限被拒绝";
                                String result = String.format(
                                        "{ \"granted\": false, \"permission\": \"microphone\", \"doNotAskAgain\": %s, \"message\": \"%s\" }",
                                        doNotAskAgain, message);
                                executeCallback(callbackName, result);
                            }
                        });
            });
        }

        @JavascriptInterface
        public void openAppSettings(String callbackName) {
            mainActivity.runOnUiThread(() -> {
                XXPermissions.startPermissionActivity(mainActivity);
                String result = "{ \"success\": true, \"message\": \"已跳转到应用设置页面\" }";
                executeCallback(callbackName, result);
            });
        }
    }

    /**
     * WebStorageCallback 回调接口
     */
    public interface WebStorageCallback {
        void onResult(String value, boolean success);
    }

    /**
     * 获取 web 端 localStorage 的所有内容
     */
    public static void getWebLocalStorage(WebStorageCallback callback) {
        if (webView != null) {
            webView.post(() -> {
                webView.evaluateJavascript("JSON.stringify(localStorage);", value -> {
                    boolean success = value != null && !"null".equals(value);
                    callback.onResult(value, success);
                });
            });
        } else {
            callback.onResult(null, false);
        }
    }

    /**
     * 获取 localStorage 中 key 为 casdoor_token 的值
     */
    public static void getCasdoorToken(WebStorageCallback callback) {
        if (webView != null) {
            webView.post(() -> {
                webView.evaluateJavascript("localStorage.getItem('casdoor_token');", value -> {
                    boolean success = value != null && !"null".equals(value);
                    callback.onResult(value, success);
                });
            });
        } else {
            callback.onResult(null, false);
        }
    }

    /**
     * 执行 JavaScript 回调
     */
    private static void executeCallback(String callbackName, String result) {
        String script = String.format("if(window.%s) { window.%s(%s); }",
                callbackName, callbackName, result);
        if (webView != null) {
            webView.post(() -> {
                webView.evaluateJavascript(script, null);
            });
        }
    }
} 