package tina.chat;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

public class NetworkDiagnosticView extends LinearLayout {
    private static final int ANIMATION_DURATION = 300;
    
    private TextView titleView;
    private TextView messageView;
    private LinearLayout statusContainer;
    private TextView retryButton;
    private TextView successMessage;
    
    // 状态指示器
    private NetworkStatusItem phoneSignalItem;
    private NetworkStatusItem networkItem;
    private NetworkStatusItem internetItem;
    private NetworkStatusItem serverItem;
    
    private OnRetryClickListener retryListener;
    private boolean isShowingSuccess = false;
    
    public interface OnRetryClickListener {
        void onRetryClick();
    }
    
    public NetworkDiagnosticView(Context context) {
        super(context);
        initView();
    }
    
    private void initView() {
        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER);
        setBackgroundColor(Color.parseColor("#F2F2F7")); // iOS系统背景色
        
        int padding = dpToPx(24);
        setPadding(padding, padding, padding, padding);
        
        // 创建卡片容器
        LinearLayout cardContainer = createCardContainer();
        
        // 创建视图组件（但不添加到this，而是添加到cardContainer）
        createTitleView(cardContainer);
        createMessageView(cardContainer);
        createStatusContainer(cardContainer);
        createSuccessMessage(cardContainer);
        createRetryButton(cardContainer);
        
        addView(cardContainer);
        
        // 初始状态为完全可见，不再使用透明度
        setVisibility(VISIBLE);
        setAlpha(1.0f);
        
        // 记录日志
        Log.d("NetworkDiagnosticView", "View initialized and set to visible");
    }
    
    private LinearLayout createCardContainer() {
        LinearLayout card = new LinearLayout(getContext());
        card.setOrientation(VERTICAL);
        card.setGravity(Gravity.CENTER);
        
        // 创建卡片背景
        GradientDrawable cardBackground = new GradientDrawable();
        cardBackground.setShape(GradientDrawable.RECTANGLE);
        cardBackground.setColor(Color.WHITE);
        cardBackground.setCornerRadius(dpToPx(16));
        cardBackground.setStroke(dpToPx(1), Color.parseColor("#E5E5EA"));
        card.setBackground(cardBackground);
        
        int cardPadding = dpToPx(24);
        card.setPadding(cardPadding, cardPadding, cardPadding, cardPadding);
        
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        cardParams.setMargins(dpToPx(16), dpToPx(16), dpToPx(16), dpToPx(16));
        card.setLayoutParams(cardParams);
        
        return card;
    }
    
    private void createStatusContainer(LinearLayout container) {
        statusContainer = new LinearLayout(getContext());
        statusContainer.setOrientation(VERTICAL);
        statusContainer.setGravity(Gravity.CENTER);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        params.bottomMargin = dpToPx(32);
        statusContainer.setLayoutParams(params);
        
        // 创建状态项
        phoneSignalItem = new NetworkStatusItem(getContext(), "📱", "手机信号");
        networkItem = new NetworkStatusItem(getContext(), "📶", "网络连接");
        internetItem = new NetworkStatusItem(getContext(), "🌐", "互联网");
        serverItem = new NetworkStatusItem(getContext(), "🖥️", "服务器");
        
        // 添加状态项和连接线
        statusContainer.addView(phoneSignalItem);
        statusContainer.addView(createConnectionLine());
        statusContainer.addView(networkItem);
        statusContainer.addView(createConnectionLine());
        statusContainer.addView(internetItem);
        statusContainer.addView(createConnectionLine());
        statusContainer.addView(serverItem);
        
        container.addView(statusContainer);
    }
    
    private View createConnectionLine() {
        // 创建一个容器来放置连接线，使其与图标列对齐
        LinearLayout lineContainer = new LinearLayout(getContext());
        lineContainer.setOrientation(HORIZONTAL);
        
        // 创建左侧空白区域（与图标容器左边距对齐）
        View leftSpacer = new View(getContext());
        LinearLayout.LayoutParams leftSpacerParams = new LinearLayout.LayoutParams(
            dpToPx(22), // 与NetworkStatusItem的左padding对齐
            dpToPx(1)
        );
        leftSpacer.setLayoutParams(leftSpacerParams);
        
        // 创建连接线
        View line = new View(getContext());
        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
        drawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
        drawable.setColor(Color.parseColor("#E5E5EA"));
        line.setBackground(drawable);
        
        LinearLayout.LayoutParams lineParams = new LinearLayout.LayoutParams(
            dpToPx(2),
            dpToPx(20)
        );
        lineParams.leftMargin = dpToPx(17); // 图标中心位置 (44/2 - 1)
        line.setLayoutParams(lineParams);
        
        lineContainer.addView(leftSpacer);
        lineContainer.addView(line);
        
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        containerParams.topMargin = dpToPx(2);
        containerParams.bottomMargin = dpToPx(2);
        lineContainer.setLayoutParams(containerParams);
        
        return lineContainer;
    }
    
    private void createSuccessMessage(LinearLayout container) {
        successMessage = new TextView(getContext());
        successMessage.setText("✅ 网络连接已恢复");
        successMessage.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
        successMessage.setTextColor(Color.parseColor("#34C759")); // iOS绿色
        successMessage.setGravity(Gravity.CENTER);
        successMessage.setTypeface(null, android.graphics.Typeface.BOLD);
        successMessage.setVisibility(GONE);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        params.bottomMargin = dpToPx(24);
        successMessage.setLayoutParams(params);
        
        container.addView(successMessage);
    }
    
    private void createTitleView(LinearLayout container) {
        titleView = new TextView(getContext());
        titleView.setText("网络连接检测");
        titleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 24);
        titleView.setTextColor(Color.parseColor("#2C2C2E")); // 使用深灰色单色
        titleView.setGravity(Gravity.CENTER);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, 
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        params.bottomMargin = dpToPx(8);
        titleView.setLayoutParams(params);
        
        container.addView(titleView);
    }
    
    private void createMessageView(LinearLayout container) {
        messageView = new TextView(getContext());
        messageView.setText("正在检测网络连接状态...");
        messageView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        messageView.setTextColor(Color.parseColor("#8E8E93")); // iOS次要标签颜色
        messageView.setGravity(Gravity.CENTER);
        messageView.setLineSpacing(dpToPx(4), 1.0f);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, 
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        params.bottomMargin = dpToPx(24);
        params.leftMargin = dpToPx(16);
        params.rightMargin = dpToPx(16);
        messageView.setLayoutParams(params);
        
        container.addView(messageView);
    }
    
    private void createRetryButton(LinearLayout container) {
        retryButton = new TextView(getContext());
        retryButton.setText("重试");
        retryButton.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17);
        retryButton.setTextColor(Color.WHITE);
        retryButton.setGravity(Gravity.CENTER);
        retryButton.setTypeface(null, android.graphics.Typeface.BOLD);
        
        // 创建圆角背景
        GradientDrawable background = new GradientDrawable();
        background.setShape(GradientDrawable.RECTANGLE);
        background.setColor(Color.parseColor("#007AFF")); // iOS蓝色
        background.setCornerRadius(dpToPx(12));
        retryButton.setBackground(background);
        
        int paddingH = dpToPx(32);
        int paddingV = dpToPx(12);
        retryButton.setPadding(paddingH, paddingV, paddingH, paddingV);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, 
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        retryButton.setLayoutParams(params);
        
        // 添加点击效果
        retryButton.setOnClickListener(v -> {
            // 点击动画
            v.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction(() -> {
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start();
                })
                .start();
            
            if (retryListener != null) {
                retryListener.onRetryClick();
            }
        });
        
        container.addView(retryButton);
    }
    
    public void setOnRetryClickListener(OnRetryClickListener listener) {
        this.retryListener = listener;
    }
    
    public void updateNetworkStatus(NetworkDiagnosticManager.NetworkStatus status) {
        if (isShowingSuccess) return;
        
        // 更新标题和消息
        if (status.isFullyConnected()) {
            titleView.setText("网络连接正常");
            messageView.setText("所有网络检查已通过");
        } else {
            titleView.setText("网络连接异常");
            messageView.setText("Tina 正在检测网络连接状态...");
        }
        
        // 更新各项状态
        phoneSignalItem.setStatus(status.hasPhoneSignal ? 
            NetworkStatusItem.Status.SUCCESS : NetworkStatusItem.Status.FAILED);
        
        // 网络连接状态
        if (status.hasWifiConnection || status.hasMobileConnection) {
            networkItem.setStatus(NetworkStatusItem.Status.SUCCESS);
            // 更新网络类型标签
            networkItem.updateLabel(status.getConnectionType());
        } else {
            networkItem.setStatus(NetworkStatusItem.Status.FAILED);
            networkItem.updateLabel("网络连接");
        }
        
        // 互联网访问状态
        if (status.hasWifiConnection || status.hasMobileConnection) {
            internetItem.setStatus(status.hasInternetAccess ? 
                NetworkStatusItem.Status.SUCCESS : NetworkStatusItem.Status.FAILED);
        } else {
            internetItem.setStatus(NetworkStatusItem.Status.DISABLED);
        }
        
        // 服务器连接状态
        if (status.hasInternetAccess) {
            serverItem.setStatus(status.canReachServer ? 
                NetworkStatusItem.Status.SUCCESS : NetworkStatusItem.Status.FAILED);
        } else {
            serverItem.setStatus(NetworkStatusItem.Status.DISABLED);
        }
    }
    
    public void showNetworkRestored() {
        isShowingSuccess = true;
        
        // 隐藏状态容器和重试按钮
        statusContainer.setVisibility(GONE);
        retryButton.setVisibility(GONE);
        
        // 显示成功消息
        titleView.setText("网络已恢复");
        messageView.setText("正在重新加载页面...");
        successMessage.setVisibility(VISIBLE);
        
        // 3秒后自动隐藏
        postDelayed(() -> {
            hideWithAnimation();
        }, 3000);
    }
    
    public void showWithAnimation() {
        // 无论当前可见性如何，都强制显示
        Log.d("NetworkDiagnosticView", "showWithAnimation called, current visibility: " + 
              (getVisibility() == VISIBLE ? "VISIBLE" : "NOT_VISIBLE") + ", alpha: " + getAlpha());
        
        // 重置状态
        isShowingSuccess = false;
        statusContainer.setVisibility(VISIBLE);
        retryButton.setVisibility(VISIBLE);
        successMessage.setVisibility(GONE);
        
        // 确保视图可见
        setVisibility(VISIBLE);
        
        // 无论当前透明度如何，都执行动画
        clearAnimation(); // 清除可能存在的动画
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(this, "alpha", getAlpha(), 1f);
        fadeIn.setDuration(ANIMATION_DURATION);
        fadeIn.start();
        
        Log.d("NetworkDiagnosticView", "Animation started to show view");
    }
    
    public void hideWithAnimation() {
        if (getVisibility() == GONE) return;
        
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        fadeOut.setDuration(ANIMATION_DURATION);
        fadeOut.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                setVisibility(GONE);
                // 重置状态
                isShowingSuccess = false;
            }
        });
        fadeOut.start();
    }
    
    private int dpToPx(int dp) {
        return (int) TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP, 
            dp, 
            getContext().getResources().getDisplayMetrics()
        );
    }
}