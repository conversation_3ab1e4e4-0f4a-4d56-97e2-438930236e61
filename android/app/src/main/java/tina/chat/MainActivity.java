package tina.chat;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.getcapacitor.BridgeActivity;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import tina.chat.push.PushServiceManager;

import java.util.List;
import tina.chat.WebViewJSInjector;

public class MainActivity extends BridgeActivity {

    private static final String TAG = "MainActivity";

    private ViewPager2 viewPager;
    private MainPagerAdapter pagerAdapter;
    private PushServiceManager pushServiceManager;
    private NetworkDiagnosticManager networkManager;

    private FunASRManager funASRManager;

    private void initWebviewJS() {
        WebViewJSInjector.init(this, getBridge().getWebView());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initWebviewJS();
        // 初始化核心组件
        initViewPager();
        initPushService();
        initNetworkMonitoring();
        setupWindowInsets();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cleanup();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (networkManager != null) {
            networkManager.startMonitoring();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (networkManager != null) {
            networkManager.stopMonitoring();
        }
    }

    /**
     * 初始化 ViewPager2 和页面管理
     */
    private void initViewPager() {
        viewPager = new ViewPager2(this);
        viewPager.setId(View.generateViewId());
        
        pagerAdapter = new MainPagerAdapter(this);
        viewPager.setAdapter(pagerAdapter);
        
        // 默认显示启动页面
        viewPager.setCurrentItem(MainPagerAdapter.PAGE_SPLASH, false);
        viewPager.setUserInputEnabled(false); // 禁用用户滑动
        
        setContentView(viewPager);
        
        // 设置页面切换监听
        setupPageListeners();
    }
    
    /**
     * 设置页面切换监听器
     */
    private void setupPageListeners() {
        viewPager.post(() -> {
            // 设置 WebView 准备就绪监听
            WebViewFragment webViewFragment = pagerAdapter.getWebViewFragment();
            if (webViewFragment != null) {
                webViewFragment.setOnWebViewReadyListener(() -> {
                    Log.d(TAG, "WebView ready, ending splash screen");
                    // 完成启动页面动画
                    SplashFragment splashFragment = pagerAdapter.getSplashFragment();
                    if (splashFragment != null) {
                        splashFragment.completeSplash();
                    }
                    // WebView 准备就绪后切换到 WebView 页面
                    new android.os.Handler().postDelayed(() -> switchToWebView(), 1000);
                });
            }
            
            // 设置网络恢复监听
            NetworkDiagnosticFragment networkFragment = pagerAdapter.getNetworkDiagnosticFragment();
            if (networkFragment != null) {
                networkFragment.setOnNetworkRestoredListener(() -> {
                    Log.d(TAG, "Network restored, refreshing WebView");
                    refreshWebViewAndSwitch();
                });
            }
        });
    }
    
    /**
     * 设置窗口插入监听
     */
    private void setupWindowInsets() {
        View rootView = getWindow().getDecorView().getRootView();
        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            Insets systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            Insets imeInsets = insets.getInsets(WindowInsetsCompat.Type.ime());
            
            // 应用系统栏和键盘插入
            v.setPadding(
                systemBarInsets.left,
                systemBarInsets.top,
                systemBarInsets.right,
                Math.max(systemBarInsets.bottom, imeInsets.bottom)
            );
            return insets;
        });
    }
    
    /**
     * 初始化网络监控
     */
    private void initNetworkMonitoring() {
        networkManager = new NetworkDiagnosticManager(this);
        networkManager.setNetworkStatusListener(new NetworkDiagnosticManager.NetworkStatusListener() {
            @Override
            public void onNetworkStatusChanged(NetworkDiagnosticManager.NetworkStatus status) {
                runOnUiThread(() -> handleNetworkStatusChange(status));
            }
            
            @Override
            public void onNetworkRestored() {
                runOnUiThread(() -> {
                    Log.d(TAG, "Network restored via global monitoring");
                    refreshWebViewAndSwitch();
                });
            }
        });
    }
    
    /**
     * 处理网络状态变化
     */
    private void handleNetworkStatusChange(NetworkDiagnosticManager.NetworkStatus status) {
        // 如果还在启动页面，不进行网络状态切换
        if (getCurrentPage() == MainPagerAdapter.PAGE_SPLASH) {
            return;
        }
        
        if (status.isFullyConnected()) {
            if (getCurrentPage() != MainPagerAdapter.PAGE_WEBVIEW) {
                switchToWebView();
            }
        } else {
            if (getCurrentPage() != MainPagerAdapter.PAGE_NETWORK_DIAGNOSTIC) {
                switchToNetworkDiagnostic();
                // 确保网络诊断界面正确显示
                ensureNetworkDiagnosticVisible();
            }
        }
    }
    
    /**
     * 刷新 WebView 并切换页面
     */
    private void refreshWebViewAndSwitch() {
        WebViewFragment webViewFragment = pagerAdapter.getWebViewFragment();
        if (webViewFragment != null) {
            webViewFragment.refreshWebView();
        }
        
        // 延迟切换回 WebView 页面
        new android.os.Handler().postDelayed(() -> switchToWebView(), 2000);
    }
    
    /**
     * 初始化推送服务
     */
    private void initPushService() {
        pushServiceManager = PushServiceManager.getInstance(this);
        pushServiceManager.initializePushService();
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        if (pushServiceManager != null) {
            pushServiceManager.cleanup();
        }
        if (networkManager != null) {
            networkManager.cleanup();
        }
    }
    
    // ========== 页面切换方法 ==========
    
    /**
     * 切换到 WebView 页面
     */
    public void switchToWebView() {
        if (viewPager != null) {
            Log.d(TAG, "Switching to WebView page");
            viewPager.setCurrentItem(MainPagerAdapter.PAGE_WEBVIEW, true);
        }
    }
    
    /**
     * 切换到网络诊断页面
     */
    public void switchToNetworkDiagnostic() {
        if (viewPager != null) {
            Log.d(TAG, "Switching to Network Diagnostic page");
            
            // 先预加载网络诊断Fragment
            NetworkDiagnosticFragment networkFragment = pagerAdapter.getNetworkDiagnosticFragment();
            if (networkFragment == null) {
                // 强制创建Fragment
                networkFragment = (NetworkDiagnosticFragment) pagerAdapter.createFragment(MainPagerAdapter.PAGE_NETWORK_DIAGNOSTIC);
            }
            
            // 切换页面
            viewPager.setCurrentItem(MainPagerAdapter.PAGE_NETWORK_DIAGNOSTIC, true);
            
            // 延迟确保诊断界面正确显示，给足够时间让Fragment完成生命周期
            new android.os.Handler().postDelayed(() -> ensureNetworkDiagnosticVisible(), 300);
            
            // 再次延迟检查，以防第一次尝试失败
            new android.os.Handler().postDelayed(() -> ensureNetworkDiagnosticVisible(), 800);
        }
    }
    
    /**
     * 获取当前页面索引
     */
    public int getCurrentPage() {
        return viewPager != null ? viewPager.getCurrentItem() : MainPagerAdapter.PAGE_SPLASH;
    }
    
    /**
     * 检查网络状态并决定显示哪个页面
     */
    public void checkNetworkAndSwitch() {
        viewPager.post(() -> {
            NetworkDiagnosticFragment networkFragment = pagerAdapter.getNetworkDiagnosticFragment();
            if (networkFragment != null && !networkFragment.isNetworkConnected()) {
                switchToNetworkDiagnostic();
                ensureNetworkDiagnosticVisible();
            } else {
                switchToWebView();
            }
        });
    }
    
    /**
     * 确保网络诊断界面正确显示
     */
    private void ensureNetworkDiagnosticVisible() {
        viewPager.post(() -> {
            NetworkDiagnosticFragment networkFragment = pagerAdapter.getNetworkDiagnosticFragment();
            if (networkFragment != null) {
                // 使用递归方法确保视图显示，最多尝试5次
                tryShowNetworkDiagnostic(networkFragment, 0, 5);
            }
        });
    }
    
    /**
     * 尝试显示网络诊断界面，如果失败则重试
     * @param fragment 网络诊断Fragment
     * @param attempt 当前尝试次数
     * @param maxAttempts 最大尝试次数
     */
    private void tryShowNetworkDiagnostic(NetworkDiagnosticFragment fragment, int attempt, int maxAttempts) {
        if (attempt >= maxAttempts) {
            Log.e(TAG, "Failed to show network diagnostic view after " + maxAttempts + " attempts");
            
            // 最后尝试：强制重新创建Fragment
            if (viewPager != null && pagerAdapter != null) {
                Log.d(TAG, "Last resort: forcing fragment recreation");
                
                // 先切换到其他页面，然后再切回来，强制重新创建Fragment
                viewPager.setCurrentItem(MainPagerAdapter.PAGE_SPLASH, false);
                
                new android.os.Handler().postDelayed(() -> {
                    viewPager.setCurrentItem(MainPagerAdapter.PAGE_NETWORK_DIAGNOSTIC, true);
                }, 100);
            }
            return;
        }
        
        View view = fragment.getView();
        if (view == null) {
            // 视图还未创建，延迟后重试
            Log.d(TAG, "Network diagnostic view not yet created, retrying... (attempt " + (attempt + 1) + ")");
            new android.os.Handler().postDelayed(() -> 
                tryShowNetworkDiagnostic(fragment, attempt + 1, maxAttempts), 200);
            return;
        }
        
        if (view instanceof NetworkDiagnosticView) {
            NetworkDiagnosticView diagnosticView = (NetworkDiagnosticView) view;
            
            // 强制设置为可见并显示动画
            Log.d(TAG, "Showing network diagnostic view with animation");
            diagnosticView.post(() -> {
                // 确保视图层次结构中的所有父视图都可见
                ViewGroup parent = (ViewGroup) diagnosticView.getParent();
                while (parent != null) {
                    parent.setVisibility(View.VISIBLE);
                    if (parent.getParent() instanceof ViewGroup) {
                        parent = (ViewGroup) parent.getParent();
                    } else {
                        break;
                    }
                }
                
                // 设置视图可见并显示动画
                diagnosticView.setVisibility(View.VISIBLE);
                diagnosticView.setAlpha(1.0f);
                diagnosticView.showWithAnimation();
                
                // 再次延迟检查，确保视图真的可见
                new android.os.Handler().postDelayed(() -> {
                    if (diagnosticView.getVisibility() != View.VISIBLE || diagnosticView.getAlpha() < 0.9f) {
                        Log.w(TAG, "View still not fully visible after animation, forcing visibility");
                        diagnosticView.setVisibility(View.VISIBLE);
                        diagnosticView.setAlpha(1.0f);
                    }
                }, 500);
            });
        } else {
            // 视图类型不匹配，延迟后重试
            Log.w(TAG, "Network diagnostic view is not of expected type, retrying... (attempt " + (attempt + 1) + ")");
            new android.os.Handler().postDelayed(() -> 
                tryShowNetworkDiagnostic(fragment, attempt + 1, maxAttempts), 200);
        }
    }

    // ========== JavaScript 接口 ==========

    // ======= 移除 PermissionInterface 和 executeCallback，迁移到 WebViewJSInjector =======
}