package tina.chat;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.Handler;
import android.os.Looper;
import android.telephony.TelephonyManager;
import android.util.Log;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class NetworkDiagnosticManager {
    private static final String TAG = "NetworkDiagnostic";
    private static final String INTERNET_CHECK_URL = "http://connectivitycheck.platform.hicloud.com/generate_204";
    private static final String SERVER_URL = "https://tina-test.bfbdata.com/new_mvp/version/check?time=";
    private static final int CHECK_INTERVAL = 10000; // 10秒检查一次
    private static final int TIMEOUT = 8000; // 8秒超时
    
    private Context context;
    private ConnectivityManager connectivityManager;
    private TelephonyManager telephonyManager;
    private NetworkCallback networkCallback;
    private ExecutorService executor;
    private Handler mainHandler;
    private Runnable periodicCheck;
    
    private NetworkStatusListener listener;
    private NetworkStatus currentStatus;
    private boolean isMonitoring = false;
    
    public static class NetworkStatus {
        public boolean hasPhoneSignal = false;
        public boolean hasWifiConnection = false;
        public boolean hasMobileConnection = false;
        public boolean hasInternetAccess = false;
        public boolean canReachServer = false;
        public String networkType = "无网络";
        public String errorMessage = "";
        
        public boolean isFullyConnected() {
            return (hasWifiConnection || hasMobileConnection) && hasInternetAccess && canReachServer;
        }
        
        public String getConnectionType() {
            if (hasWifiConnection) return "WiFi";
            if (hasMobileConnection) return "移动网络";
            return "无网络";
        }
    }
    
    public interface NetworkStatusListener {
        void onNetworkStatusChanged(NetworkStatus status);
        void onNetworkRestored();
    }
    
    public NetworkDiagnosticManager(Context context) {
        this.context = context;
        this.connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        this.telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        this.executor = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.currentStatus = new NetworkStatus();
        
        initNetworkCallback();
        initPeriodicCheck();
    }
    
    private void initNetworkCallback() {
        networkCallback = new NetworkCallback();
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
    }
    
    private void initPeriodicCheck() {
        if (periodicCheck != null) return;
        periodicCheck = new Runnable() {
            @Override
            public void run() {
                if (isMonitoring) {
                    checkFullNetworkStatus();
                    mainHandler.postDelayed(this, CHECK_INTERVAL);
                }
            }
        };
    }
    
    public void setNetworkStatusListener(NetworkStatusListener listener) {
        this.listener = listener;
    }
    
    public void startMonitoring() {
        if (isMonitoring) return;
        
        isMonitoring = true;
        Log.d(TAG, "开始详细网络监控");
        
        // 立即检查一次
        checkFullNetworkStatus();
        
        // 开始定期检查
        mainHandler.post(periodicCheck);
    }
    
    public void stopMonitoring() {
        isMonitoring = false;
        mainHandler.removeCallbacks(periodicCheck);
        Log.d(TAG, "停止网络监控");
    }
    
    private void checkFullNetworkStatus() {
        executor.execute(() -> {
            NetworkStatus newStatus = new NetworkStatus();
            
            // 1. 检查手机信号
            newStatus.hasPhoneSignal = checkPhoneSignal();
            
            // 2. 检查网络连接类型
            checkNetworkConnection(newStatus);
            
            // 3. 如果有网络连接，检查互联网访问
            if (newStatus.hasWifiConnection || newStatus.hasMobileConnection) {
                newStatus.hasInternetAccess = checkInternetAccess();
                
                // 4. 如果有互联网访问，检查服务器连接
                if (newStatus.hasInternetAccess) {
                    newStatus.canReachServer = checkServerConnectivity();
                }
            }
            
            // 设置网络类型描述
            newStatus.networkType = newStatus.getConnectionType();
            
            final NetworkStatus finalStatus = newStatus;
            mainHandler.post(() -> {
                boolean wasFullyConnected = currentStatus.isFullyConnected();
                boolean isNowFullyConnected = finalStatus.isFullyConnected();
                
                currentStatus = finalStatus;
                
                if (listener != null) {
                    listener.onNetworkStatusChanged(finalStatus);
                    
                    // 如果从断开状态恢复到完全连接，触发恢复回调
                    if (!wasFullyConnected && isNowFullyConnected) {
                        listener.onNetworkRestored();
                    }
                }
                
                Log.d(TAG, String.format("网络状态: 信号=%s, WiFi=%s, 移动=%s, 互联网=%s, 服务器=%s", 
                      finalStatus.hasPhoneSignal, finalStatus.hasWifiConnection, 
                      finalStatus.hasMobileConnection, finalStatus.hasInternetAccess, 
                      finalStatus.canReachServer));
            });
        });
    }
    
    private boolean checkPhoneSignal() {
        try {
            if (telephonyManager == null) return false;
            
            int simState = telephonyManager.getSimState();
            return simState == TelephonyManager.SIM_STATE_READY;
        } catch (Exception e) {
            Log.w(TAG, "检查手机信号失败", e);
            return false;
        }
    }
    
    private void checkNetworkConnection(NetworkStatus status) {
        try {
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork == null) {
                status.hasWifiConnection = false;
                status.hasMobileConnection = false;
                return;
            }
            
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
            if (capabilities == null) {
                status.hasWifiConnection = false;
                status.hasMobileConnection = false;
                return;
            }
            
            status.hasWifiConnection = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
            status.hasMobileConnection = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR);
            
        } catch (Exception e) {
            Log.e(TAG, "检查网络连接类型失败", e);
            status.hasWifiConnection = false;
            status.hasMobileConnection = false;
        }
    }
    
    private boolean checkInternetAccess() {
        try {
            URL url = new URL(INTERNET_CHECK_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(false);
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            boolean hasInternet = responseCode == 204;
            Log.d(TAG, String.format("互联网连接检查: %s, 响应码: %d", INTERNET_CHECK_URL, responseCode));
            return hasInternet;
            
        } catch (IOException e) {
            Log.w(TAG, "互联网连接检查失败: " + e.getMessage());
            return false;
        } catch (Exception e) {
            Log.e(TAG, "互联网连接检查异常", e);
            return false;
        }
    }
    
    private boolean checkServerConnectivity() {
        try {
            String urlString = SERVER_URL + System.currentTimeMillis();
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            boolean reachable = responseCode >= 200 && responseCode < 400;
            Log.d(TAG, String.format("服务器连接检查: %s, 响应码: %d", urlString, responseCode));
            return reachable;
            
        } catch (IOException e) {
            Log.w(TAG, "服务器连接检查失败: " + e.getMessage());
            return false;
        } catch (Exception e) {
            Log.e(TAG, "服务器连接检查异常", e);
            return false;
        }
    }
    
    public NetworkStatus getCurrentStatus() {
        return currentStatus;
    }
    
    public boolean isConnected() {
        return currentStatus != null && currentStatus.isFullyConnected();
    }
    
    public void cleanup() {
        stopMonitoring();
        
        if (networkCallback != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            } catch (Exception e) {
                Log.w(TAG, "注销网络回调失败", e);
            }
        }
        
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
    
    private class NetworkCallback extends ConnectivityManager.NetworkCallback {
        @Override
        public void onAvailable(Network network) {
            Log.d(TAG, "网络连接可用");
            if (isMonitoring) {
                checkFullNetworkStatus();
            }
        }
        
        @Override
        public void onLost(Network network) {
            Log.d(TAG, "网络连接丢失");
            mainHandler.post(() -> {
                NetworkStatus lostStatus = new NetworkStatus();
                lostStatus.hasPhoneSignal = checkPhoneSignal();
                lostStatus.networkType = "无网络";
                currentStatus = lostStatus;
                
                if (listener != null) {
                    listener.onNetworkStatusChanged(lostStatus);
                }
            });
        }
        
        @Override
        public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
            boolean hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
            boolean isValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
            
            Log.d(TAG, String.format("网络能力变化: Internet=%s, Validated=%s", hasInternet, isValidated));
            
            if (isMonitoring && hasInternet && isValidated) {
                checkFullNetworkStatus();
            }
        }
    }
}