# MainActivity 重构说明

## 主要改进

### 1. 修复加载顺序
- **之前**: SplashFragment 显示 → SplashFragment 结束 → 开始加载 WebViewFragment
- **现在**: SplashFragment 和 WebViewFragment 同时开始加载 → WebViewFragment 准备就绪 → 结束 SplashFragment

### 2. 移除重复代码
- 合并了重复的网络监听逻辑
- 简化了页面切换方法
- 移除了不必要的延迟重试逻辑

### 3. 优化初始化流程
- 将初始化逻辑分解为独立的方法
- 统一资源清理逻辑
- 简化监听器设置

## 核心变化

### MainActivity.java
```java
// 核心初始化方法
private void initViewPager()           // ViewPager2 和页面管理
private void initPushService()         // 推送服务初始化  
private void initNetworkMonitoring()   // 网络监控初始化
private void setupWindowInsets()       // 窗口插入设置

// 页面切换方法
public void switchToWebView()          // 切换到 WebView
public void switchToNetworkDiagnostic() // 切换到网络诊断
public int getCurrentPage()            // 获取当前页面

// 清理方法
private void cleanup()                 // 统一资源清理
```

### WebViewFragment.java
- 增加了 `OnWebViewReadyListener` 接口
- WebView 初始化完成后延迟 1 秒通知准备就绪
- 确保 WebView 完全加载后再结束启动页面

### SplashFragment.java  
- 移除了自动完成逻辑
- 增加了 `completeSplash()` 方法供外部调用
- 启动页面现在由 WebView 准备状态控制

## 加载时序

```
应用启动
    ↓
初始化 ViewPager2
    ↓
显示 SplashFragment (开始动画)
    ↓
同时开始加载 WebViewFragment
    ↓
WebViewFragment 初始化完成
    ↓
延迟 1 秒确保页面完全加载
    ↓
调用 SplashFragment.completeSplash()
    ↓
延迟 1 秒后切换到 WebView 页面
```

## 网络监控逻辑

- 统一的网络状态监听器
- 只在非启动页面时进行网络状态切换
- 网络恢复时自动刷新 WebView 并切换回去

## 资源管理

- 统一的 `cleanup()` 方法
- 在 `onDestroy()` 中清理所有资源
- 在 `onPause()/onResume()` 中管理网络监控

这个重构版本更加简洁、逻辑清晰，并且修复了原有的加载顺序问题。