package tina.chat.push;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import com.huawei.hms.aaid.HmsInstanceId;
import com.huawei.hms.push.HmsMessaging;
import com.huawei.hms.common.ApiException;

/**
 * 华为推送提供商实现
 */
public class HuaweiPushProvider implements PushProvider {
    
    private static final String TAG = "HuaweiPushProvider";
    private static final String APP_ID = "114745813";
    private static final String TOKEN_SCOPE = "HCM";
    
    private Context context;
    private TokenCallback currentTokenCallback;
    
    public HuaweiPushProvider(Context context) {
        this.context = context.getApplicationContext();
    }
    
    @Override
    public Type getType() {
        return Type.HUAWEI;
    }
    
    @Override
    public void initialize() {
        Log.i(TAG, "开始初始化华为推送服务...");
        
        // 启用华为推送自动初始化
        setAutoInitEnabled(true);
        
        // 延迟获取Token进行测试
        new android.os.Handler().postDelayed(() -> {
            Log.i(TAG, "开始自动获取Token...");
            getToken(new TokenCallback() {
                @Override
                public void onSuccess(String token) {
                    Log.i(TAG, "初始化时Token获取成功");
                    // 通知PushManager Token已获取
                    notifyTokenReceived(token);
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "初始化时Token获取失败: " + error);
                }
            });
        }, 3000);
    }
    
    @Override
    public void getToken(TokenCallback callback) {
        this.currentTokenCallback = callback;
        
        new Thread(() -> {
            try {
                Log.i(TAG, "正在获取Token，APP_ID: " + APP_ID + ", TOKEN_SCOPE: " + TOKEN_SCOPE);
                String token = HmsInstanceId.getInstance(context).getToken(APP_ID, TOKEN_SCOPE);
                
                if (!TextUtils.isEmpty(token)) {
                    Log.i(TAG, "Token获取成功，长度: " + token.length());
                    if (callback != null) {
                        callback.onSuccess(token);
                    }
                } else {
                    Log.w(TAG, "Token为空");
                    if (callback != null) {
                        callback.onError("Token为空");
                    }
                }
            } catch (ApiException e) {
                String error = "get token failed, error code: " + e.getStatusCode() + ", message: " + e.getMessage();
                Log.e(TAG, error);
                if (callback != null) {
                    callback.onError(error);
                }
            } catch (Exception e) {
                String error = "get token failed with unexpected error: " + e.getMessage();
                Log.e(TAG, error);
                if (callback != null) {
                    callback.onError(error);
                }
            }
        }).start();
    }
    
    @Override
    public void deleteToken(TokenCallback callback) {
        new Thread(() -> {
            try {
                HmsInstanceId.getInstance(context).deleteToken(APP_ID, TOKEN_SCOPE);
                Log.i(TAG, "token deleted successfully");
                if (callback != null) {
                    callback.onSuccess("Token删除成功");
                }
            } catch (ApiException e) {
                String error = "delete token failed: " + e.getMessage();
                Log.e(TAG, error);
                if (callback != null) {
                    callback.onError(error);
                }
            } catch (Exception e) {
                String error = "delete token failed with unexpected error: " + e.getMessage();
                Log.e(TAG, error);
                if (callback != null) {
                    callback.onError(error);
                }
            }
        }).start();
    }
    
    @Override
    public void setAutoInitEnabled(boolean enabled) {
        try {
            HmsMessaging.getInstance(context).setAutoInitEnabled(enabled);
            Log.i(TAG, "华为推送自动初始化已" + (enabled ? "启用" : "禁用"));
        } catch (Exception e) {
            Log.e(TAG, "设置自动初始化失败: " + e.getMessage());
        }
    }
    
    @Override
    public void cleanup() {
        Log.i(TAG, "清理华为推送资源");
        currentTokenCallback = null;
    }
    
    /**
     * 通知Token已接收（由HuaweiPushService调用）
     */
    public void notifyTokenReceived(String token) {
        Log.i(TAG, "=== Token接收通知 ===");
        Log.i(TAG, "Token: " + token);
        Log.i(TAG, "Token长度: " + token.length());
        Log.i(TAG, "Token前20位: " + token.substring(0, Math.min(token.length(), 20)));
        Log.i(TAG, "Token后20位: " + token.substring(Math.max(0, token.length() - 20)));
        Log.i(TAG, "=== Token通知完成 ===");
        
        // 通知PushManager
        PushManager pushManager = PushManager.getInstance(context);
        if (pushManager.getTokenListener() != null) {
            pushManager.getTokenListener().onTokenReceived(token, Type.HUAWEI);
        }
    }
    
    /**
     * 通知Token错误（由HuaweiPushService调用）
     */
    public void notifyTokenError(String error) {
        Log.e(TAG, "Token错误通知: " + error);
        
        // 通知PushManager
        PushManager pushManager = PushManager.getInstance(context);
        if (pushManager.getTokenListener() != null) {
            pushManager.getTokenListener().onTokenError(error, Type.HUAWEI);
        }
    }
}