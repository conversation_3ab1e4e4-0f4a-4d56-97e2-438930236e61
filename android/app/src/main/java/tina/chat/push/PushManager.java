package tina.chat.push;

import android.content.Context;
import android.util.Log;
import java.util.HashMap;
import java.util.Map;

/**
 * 推送管理器
 * 统一管理多种推送提供商
 */
public class PushManager {
    
    private static final String TAG = "PushManager";
    private static PushManager instance;
    
    private Context context;
    private Map<PushProvider.Type, PushProvider> providers;
    private PushProvider currentProvider;
    private PushTokenListener tokenListener;
    
    private PushManager(Context context) {
        this.context = context.getApplicationContext();
        this.providers = new HashMap<>();
    }
    
    public static synchronized PushManager getInstance(Context context) {
        if (instance == null) {
            instance = new PushManager(context);
        }
        return instance;
    }
    
    /**
     * 注册推送提供商
     */
    public void registerProvider(PushProvider provider) {
        if (provider != null) {
            providers.put(provider.getType(), provider);
            Log.i(TAG, "注册推送提供商: " + provider.getType());
        }
    }
    
    /**
     * 初始化推送服务
     * 自动选择可用的推送提供商
     */
    public void initialize() {
        // 根据设备类型选择合适的推送提供商
        PushProvider.Type preferredType = detectPreferredProvider();
        
        if (preferredType != null && providers.containsKey(preferredType)) {
            currentProvider = providers.get(preferredType);
            Log.i(TAG, "选择推送提供商: " + preferredType);
            
            try {
                currentProvider.initialize();
                Log.i(TAG, "推送服务初始化成功");
            } catch (Exception e) {
                Log.e(TAG, "推送服务初始化失败: " + e.getMessage());
                // 尝试其他提供商
                tryFallbackProvider();
            }
        } else {
            Log.w(TAG, "未找到合适的推送提供商");
        }
    }
    
    /**
     * 获取推送Token
     */
    public void getToken() {
        if (currentProvider != null) {
            currentProvider.getToken(new PushProvider.TokenCallback() {
                @Override
                public void onSuccess(String token) {
                    Log.i(TAG, "Token获取成功: " + token.substring(0, Math.min(token.length(), 20)) + "...");
                    if (tokenListener != null) {
                        tokenListener.onTokenReceived(token, currentProvider.getType());
                    }
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "Token获取失败: " + error);
                    if (tokenListener != null) {
                        tokenListener.onTokenError(error, currentProvider.getType());
                    }
                }
            });
        } else {
            Log.w(TAG, "当前没有可用的推送提供商");
        }
    }
    
    /**
     * 删除推送Token
     */
    public void deleteToken() {
        if (currentProvider != null) {
            currentProvider.deleteToken(new PushProvider.TokenCallback() {
                @Override
                public void onSuccess(String result) {
                    Log.i(TAG, "Token删除成功");
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "Token删除失败: " + error);
                }
            });
        }
    }
    
    /**
     * 设置自动初始化状态
     */
    public void setAutoInitEnabled(boolean enabled) {
        if (currentProvider != null) {
            currentProvider.setAutoInitEnabled(enabled);
            Log.i(TAG, "设置自动初始化: " + enabled);
        }
    }
    
    /**
     * 设置Token监听器
     */
    public void setTokenListener(PushTokenListener listener) {
        this.tokenListener = listener;
    }
    
    /**
     * 获取Token监听器
     */
    public PushTokenListener getTokenListener() {
        return this.tokenListener;
    }
    
    /**
     * 获取当前推送提供商类型
     */
    public PushProvider.Type getCurrentProviderType() {
        return currentProvider != null ? currentProvider.getType() : null;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (currentProvider != null) {
            currentProvider.cleanup();
        }
        for (PushProvider provider : providers.values()) {
            provider.cleanup();
        }
        providers.clear();
        currentProvider = null;
        tokenListener = null;
    }
    
    /**
     * 检测首选的推送提供商
     */
    private PushProvider.Type detectPreferredProvider() {
        // 根据设备品牌或系统特性选择推送提供商
        String manufacturer = android.os.Build.MANUFACTURER.toLowerCase();
        
        if (manufacturer.contains("huawei") || manufacturer.contains("honor")) {
            return PushProvider.Type.HUAWEI;
        } else if (manufacturer.contains("xiaomi") || manufacturer.contains("redmi")) {
            return PushProvider.Type.XIAOMI;
        } else if (manufacturer.contains("oppo") || manufacturer.contains("oneplus")) {
            return PushProvider.Type.OPPO;
        } else if (manufacturer.contains("vivo")) {
            return PushProvider.Type.VIVO;
        } else {
            // 默认使用Firebase（Google Play Services）
            return PushProvider.Type.FIREBASE;
        }
    }
    
    /**
     * 尝试备用推送提供商
     */
    private void tryFallbackProvider() {
        for (PushProvider.Type type : providers.keySet()) {
            if (type != currentProvider.getType()) {
                try {
                    PushProvider fallbackProvider = providers.get(type);
                    fallbackProvider.initialize();
                    currentProvider = fallbackProvider;
                    Log.i(TAG, "切换到备用推送提供商: " + type);
                    return;
                } catch (Exception e) {
                    Log.w(TAG, "备用推送提供商初始化失败: " + type + ", " + e.getMessage());
                }
            }
        }
        Log.e(TAG, "所有推送提供商初始化失败");
    }
    
    /**
     * 推送Token监听器接口
     */
    public interface PushTokenListener {
        void onTokenReceived(String token, PushProvider.Type providerType);
        void onTokenError(String error, PushProvider.Type providerType);
    }
}