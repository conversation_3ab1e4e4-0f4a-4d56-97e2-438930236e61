package tina.chat.push;

import android.util.Log;
import com.huawei.hms.push.HmsMessageService;
import com.huawei.hms.push.RemoteMessage;

/**
 * 华为推送服务实现类
 * 用于接收推送消息和Token更新
 */
public class HuaweiPushService extends HmsMessageService {
    private static final String TAG = "HuaweiPushService";

    @Override
    public void onNewToken(String token) {
        super.onNewToken(token);
        Log.i(TAG, "收到新的Push Token: " + token);
        
        // 通知HuaweiPushProvider
        notifyProviderTokenReceived(token);
        
        // 将token发送到您的服务器
        sendTokenToServer(token);
    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        Log.i(TAG, "onMessageReceived is called");

        // 判断消息是否为空
        if (remoteMessage == null) {
            Log.e(TAG, "Received message entity is null!");
            return;
        }

        // 获取消息内容
        Log.i(TAG, "get Data: " + remoteMessage.getData()
                + "\n getFrom: " + remoteMessage.getFrom()
                + "\n getTo: " + remoteMessage.getTo()
                + "\n getMessageId: " + remoteMessage.getMessageId()
                + "\n getSentTime: " + remoteMessage.getSentTime()
                + "\n getDataMap: " + remoteMessage.getDataOfMap()
                + "\n getMessageType: " + remoteMessage.getMessageType()
                + "\n getTtl: " + remoteMessage.getTtl());

        // 处理消息
        Boolean judgeWhetherIn10s = false;
        // 如果消息在10秒内没有处理，需要您自己创建新任务处理
        if (judgeWhetherIn10s) {
            startWorkManagerJob(remoteMessage);
        } else {
            // 10秒内处理消息
            processWithin10s(remoteMessage);
        }
    }

    /**
     * 启动后台任务处理消息
     */
    private void startWorkManagerJob(RemoteMessage remoteMessage) {
        Log.d(TAG, "Start new job processing.");
        // TODO: 实现后台任务处理逻辑
        // 例如：使用WorkManager或JobScheduler处理耗时操作
        handleMessageInBackground(remoteMessage);
    }

    /**
     * 在10秒内处理消息
     */
    private void processWithin10s(RemoteMessage remoteMessage) {
        Log.d(TAG, "Processing now.");
        // 立即处理消息
        handleMessageImmediately(remoteMessage);
    }

    /**
     * 立即处理消息
     */
    private void handleMessageImmediately(RemoteMessage remoteMessage) {
        if (remoteMessage != null) {
            // 获取消息数据
            String data = remoteMessage.getData();
            Log.i(TAG, "消息数据: " + data);

            // 获取消息ID
            String messageId = remoteMessage.getMessageId();
            Log.i(TAG, "消息ID: " + messageId);

            // 获取消息类型
            String messageType = remoteMessage.getMessageType();
            Log.i(TAG, "消息类型: " + messageType);

            // 获取发送时间
            long sentTime = remoteMessage.getSentTime();
            Log.i(TAG, "发送时间: " + sentTime);

            // 获取TTL
            int ttl = remoteMessage.getTtl();
            Log.i(TAG, "TTL: " + ttl);

            // 获取Token
            String token = remoteMessage.getToken();
            Log.i(TAG, "Token: " + token);

            // 获取通知信息
            RemoteMessage.Notification notification = remoteMessage.getNotification();
            if (notification != null) {
                Log.i(TAG, "通知标题: " + notification.getTitle());
                Log.i(TAG, "通知内容: " + notification.getBody());
            }

            // 处理不同类型的消息
            handleMessageByType(remoteMessage);
        }
    }

    /**
     * 后台处理消息
     */
    private void handleMessageInBackground(RemoteMessage remoteMessage) {
        // TODO: 实现后台处理逻辑
        Log.i(TAG, "后台处理消息: " + remoteMessage.getMessageId());
    }

    /**
     * 根据消息类型处理消息
     */
    private void handleMessageByType(RemoteMessage remoteMessage) {
        String messageType = remoteMessage.getMessageType();

        if ("notification".equals(messageType)) {
            // 处理通知栏消息
            handleNotificationMessage(remoteMessage);
        } else if ("data".equals(messageType)) {
            // 处理透传消息
            handleDataMessage(remoteMessage);
        } else {
            // 处理其他类型消息
            Log.i(TAG, "处理未知类型消息: " + messageType);
        }
    }

    /**
     * 处理通知栏消息
     */
    private void handleNotificationMessage(RemoteMessage remoteMessage) {
        Log.i(TAG, "=== 处理通知栏消息 ===");
        RemoteMessage.Notification notification = remoteMessage.getNotification();
        if (notification != null) {
            Log.i(TAG, "通知标题: " + notification.getTitle());
            Log.i(TAG, "通知内容: " + notification.getBody());
            Log.i(TAG, "通知图标: " + notification.getIcon());
            Log.i(TAG, "通知颜色: " + notification.getColor());
            Log.i(TAG, "通知声音: " + notification.getSound());
            Log.i(TAG, "通知标签: " + notification.getTag());
            Log.i(TAG, "通知点击动作: " + notification.getClickAction());
            Log.i(TAG, "通知意图URI: " + notification.getIntentUri());
            Log.i(TAG, "通知渠道ID: " + notification.getChannelId());
            Log.i(TAG, "通知图片URL: " + notification.getImageUrl());
            Log.i(TAG, "通知链接: " + notification.getLink());
            Log.i(TAG, "通知通知ID: " + notification.getNotifyId());
            // 注意：以下方法可能在某些SDK版本中不可用
            // Log.i(TAG, "通知优先级: " + notification.getPriority());
            // Log.i(TAG, "通知重要性: " + notification.getImportance());
            // Log.i(TAG, "通知震动: " + notification.getVibrateTimings());
            // Log.i(TAG, "通知可见性: " + notification.getVisibility());
        }

        // TODO: 实现通知栏消息的具体处理逻辑
        // 例如：更新UI、显示自定义通知等
    }

    /**
     * 处理透传消息
     */
    private void handleDataMessage(RemoteMessage remoteMessage) {
        Log.i(TAG, "=== 处理透传消息 ===");
        String data = remoteMessage.getData();
        Log.i(TAG, "透传数据: " + data);

        // TODO: 实现透传消息的具体处理逻辑
        // 例如：解析JSON数据、更新应用状态等

        // 示例：解析JSON数据
        try {
            // 这里可以添加JSON解析逻辑
            Log.i(TAG, "透传消息处理完成");
        } catch (Exception e) {
            Log.e(TAG, "处理透传消息失败: " + e.getMessage());
        }
    }

    /**
     * 将Token发送到服务器
     */
    private void sendTokenToServer(String token) {
        // 实现将token发送到您的服务器的逻辑
        Log.i(TAG, "发送token到服务器: " + token);

        // TODO: 在这里实现您的服务器通信逻辑
        // 例如：使用HTTP请求将token发送到您的后端服务
        // 建议使用异步方式，避免阻塞主线程
    }

    @Override
    public void onTokenError(Exception e) {
        super.onTokenError(e);
        Log.e(TAG, "Token获取失败: " + e.getMessage());
        
        // 通知HuaweiPushProvider
        notifyProviderTokenError(e.getMessage());
    }
    
    /**
     * 通知HuaweiPushProvider Token已接收
     */
    private void notifyProviderTokenReceived(String token) {
        try {
            // 获取PushManager实例并通知Token接收
            PushManager pushManager = PushManager.getInstance(getApplicationContext());
            if (pushManager.getCurrentProviderType() == PushProvider.Type.HUAWEI) {
                // 直接通过PushManager的TokenListener通知
                if (pushManager.getTokenListener() != null) {
                    pushManager.getTokenListener().onTokenReceived(token, PushProvider.Type.HUAWEI);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "通知Token接收失败: " + e.getMessage());
        }
    }
    
    /**
     * 通知HuaweiPushProvider Token错误
     */
    private void notifyProviderTokenError(String error) {
        try {
            // 获取PushManager实例并通知Token错误
            PushManager pushManager = PushManager.getInstance(getApplicationContext());
            if (pushManager.getCurrentProviderType() == PushProvider.Type.HUAWEI) {
                // 直接通过PushManager的TokenListener通知
                if (pushManager.getTokenListener() != null) {
                    pushManager.getTokenListener().onTokenError(error, PushProvider.Type.HUAWEI);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "通知Token错误失败: " + e.getMessage());
        }
    }

    @Override
    public void onMessageSent(String msgId) {
        super.onMessageSent(msgId);
        Log.i(TAG, "消息发送成功，消息ID: " + msgId);
    }

    @Override
    public void onSendError(String msgId, Exception e) {
        super.onSendError(msgId, e);
        Log.e(TAG, "消息发送失败，消息ID: " + msgId + ", 错误: " + e.getMessage());
    }
}