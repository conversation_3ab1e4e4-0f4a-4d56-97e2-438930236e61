# 推送服务架构

这个包含了重构后的推送服务架构，支持多种推送提供商的统一管理。

## 架构概述

### 核心组件

1. **PushServiceManager** - 推送服务管理器（主入口）
   - 提供一站式推送服务初始化和管理
   - MainActivity只需调用一个方法即可完成所有推送相关工作
   - 自动注册所有可用的推送提供商
   - 内置默认的Token处理逻辑

2. **PushProvider** - 推送提供商接口
   - 定义了所有推送提供商需要实现的基本功能
   - 支持华为、Firebase、小米、OPPO、VIVO等推送服务

3. **PushManager** - 推送管理器（底层核心）
   - 统一管理多种推送提供商
   - 自动选择合适的推送提供商
   - 提供统一的API接口

4. **HuaweiPushProvider** - 华为推送提供商实现
   - 实现华为推送服务的具体功能
   - 处理Token获取、删除等操作

5. **HuaweiPushService** - 华为推送服务
   - 继承HmsMessageService
   - 接收推送消息和Token更新

## 使用方法

### 在MainActivity中初始化（超级简单！）

```java
/**
 * 初始化推送服务
 * 现在只需要两行代码即可完成所有推送相关的初始化工作
 */
private void initPushService() {
    pushServiceManager = PushServiceManager.getInstance(this);
    pushServiceManager.initializePushService();
}

// 在onDestroy中清理资源
@Override
public void onDestroy() {
    super.onDestroy();
    if (pushServiceManager != null) {
        pushServiceManager.cleanup();
    }
}
```

### 高级用法（可选）

```java
// 手动刷新Token
pushServiceManager.refreshToken();

// 删除Token
pushServiceManager.deleteToken();

// 设置自动初始化状态
pushServiceManager.setAutoInitEnabled(true);

// 检查是否已初始化
boolean isInitialized = pushServiceManager.isInitialized();

// 获取当前推送提供商类型
PushProvider.Type currentType = pushServiceManager.getCurrentProviderType();
```

### 添加新的推送提供商

1. 实现PushProvider接口
2. 在PushManager中注册新的提供商
3. PushManager会自动根据设备类型选择合适的提供商

```java
// 示例：添加Firebase推送提供商
FirebasePushProvider firebaseProvider = new FirebasePushProvider(this);
pushManager.registerProvider(firebaseProvider);
```

## 支持的推送提供商

- ✅ **华为推送** (Huawei Push Kit)
- 🚧 **Firebase推送** (Firebase Cloud Messaging) - 示例实现
- 🔄 **小米推送** (Mi Push) - 待实现
- 🔄 **OPPO推送** (OPPO Push) - 待实现
- 🔄 **VIVO推送** (VIVO Push) - 待实现

## 自动选择逻辑

PushManager会根据设备制造商自动选择合适的推送提供商：

- 华为/荣耀设备 → 华为推送
- 小米/红米设备 → 小米推送
- OPPO/一加设备 → OPPO推送
- VIVO设备 → VIVO推送
- 其他设备 → Firebase推送

## 优势

1. **极简使用** - MainActivity只需调用一个方法即可完成所有推送初始化
2. **统一接口** - 所有推送提供商使用相同的API
3. **自动选择** - 根据设备类型自动选择最佳推送服务
4. **易于扩展** - 添加新的推送提供商只需实现PushProvider接口
5. **故障转移** - 如果首选提供商初始化失败，会自动尝试其他提供商
6. **资源管理** - 统一的资源清理和生命周期管理
7. **内置重试** - 自动处理Token获取失败的重试逻辑
8. **完全封装** - 所有推送相关逻辑都封装在push包中，MainActivity完全解耦
9. **默认处理** - 内置默认的Token处理和服务器通信逻辑
10. **易于维护** - 清晰的分层架构，便于后续维护和扩展

## 注意事项

1. 确保在AndroidManifest.xml中正确配置推送服务
2. 添加相应推送SDK的依赖
3. 在应用销毁时调用pushManager.cleanup()清理资源
4. Token的服务器端处理需要区分不同的推送提供商类型