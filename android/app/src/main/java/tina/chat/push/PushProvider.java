package tina.chat.push;

/**
 * 推送提供商接口
 * 定义所有推送提供商需要实现的基本功能
 */
public interface PushProvider {
    
    /**
     * 推送提供商类型枚举
     */
    enum Type {
        HUAWEI,
        FIREBASE,
        XIAOMI,
        OPPO,
        VIVO
    }
    
    /**
     * 获取推送提供商类型
     */
    Type getType();
    
    /**
     * 初始化推送服务
     */
    void initialize();
    
    /**
     * 获取推送Token
     */
    void getToken(TokenCallback callback);
    
    /**
     * 删除推送Token
     */
    void deleteToken(TokenCallback callback);
    
    /**
     * 设置自动初始化状态
     */
    void setAutoInitEnabled(boolean enabled);
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * Token回调接口
     */
    interface TokenCallback {
        void onSuccess(String token);
        void onError(String error);
    }
}