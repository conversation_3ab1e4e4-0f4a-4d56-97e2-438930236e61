apply plugin: 'com.android.application'

android {
    namespace "tina.chat"
    compileSdk rootProject.ext.compileSdkVersion
    defaultConfig {
        applicationId "tina.chat"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 117
        versionName "1.3.14"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }
    
    // 启用 buildConfig 功能
    buildFeatures {
        buildConfig true
    }
    
    signingConfigs {
        release {
            storeFile file("tina-app-key.keystore")
            storePassword "tinachat2024"
            keyAlias "tina-app-key"
            keyPassword "tinachat2024"
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable false
            buildConfigField "boolean", "ENABLE_DEBUG_LOGGING", "false"
        }
        debug {
            buildConfigField "boolean", "ENABLE_DEBUG_LOGGING", "true"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation "androidx.fragment:fragment:$androidxFragmentVersion"
    implementation "androidx.viewpager2:viewpager2:1.0.0"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
    // 添加 XXPermissions 权限库 - 使用更稳定的版本
    implementation 'com.github.getActivity:XXPermissions:18.6'
    // 添加 OkHttp 用于 WebSocket 连接
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    // 添加华为Push SDK
    implementation 'com.huawei.hms:push:6.11.0.300'
    // 添加HMS Core Installer SDK，自动引导用户下载HMS Core APK
    implementation 'com.huawei.hms:hmscoreinstaller:6.7.0.300'
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}

try {
    def agconnectServicesJSON = file('agconnect-services.json')
    if (agconnectServicesJSON.text) {
        apply plugin: 'com.huawei.agconnect'
    }
} catch(Exception e) {
    logger.info("agconnect-services.json not found, huawei agconnect plugin not applied. Huawei Push Notifications won't work")
}
