pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        // 配置HMS Core SDK的Maven仓地址
        maven { url 'https://developer.huawei.com/repo/' }
    }
}
dependencyResolutionManagement {
    repositories {
        google()
        mavenCentral()
        // 配置HMS Core SDK的Maven仓地址
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

include ':app'
include ':capacitor-cordova-android-plugins'
project(':capacitor-cordova-android-plugins').projectDir = new File('./capacitor-cordova-android-plugins/')

apply from: 'capacitor.settings.gradle'