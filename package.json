{"name": "tina-webchat", "private": true, "version": "1.3.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "build:android": "BUILD_ENV=release vite build", "preview": "vite preview", "format:check": "prettier --check .", "format": "prettier --write .", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "ionic:build": "pnpm run build", "ionic:serve": "vite --host 0.0.0.0", "ionic:dev": "ionic capacitor run android -l --external", "ionic:sync": "ionic capacitor sync android", "ionic:open": "ionic capacitor open android", "android:simple": "bash scripts/simple-android-build.sh", "android:apk": "pnpm build && npx cap sync android && cd android && ./gradlew assembleRelease", "android:version-auto": "node scripts/update-version.cjs"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@capacitor/android": "7.4.0", "@capacitor/app": "7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/cli": "7.4.0", "@capacitor/core": "7.4.0", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "^7.4.2", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@capacitor/toast": "^7.0.1", "@capawesome/capacitor-app-update": "^7.0.1", "@emotion/react": "11.11.4", "@faker-js/faker": "^8.4.1", "@floating-ui/react": "^0.26.28", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@ionic/core": "^8.6.2", "@ionic/react": "^8.6.2", "@ionic/react-router": "^8.6.2", "@microlink/react-json-view": "^1.26.2", "@microsoft/fetch-event-source": "^2.0.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "ahooks": "^3.9.0", "animate.css": "^4.1.1", "antd": "5.18.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "dequal": "^2.0.3", "dexie": "^4.0.11", "event-source-plus": "^0.1.11", "fast-xml-parser": "^5.2.5", "file-saver": "^2.0.5", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^2.7.3", "ionicons": "^8.0.9", "jotai": "^2.12.5", "jotai-effect": "^1.1.6", "jotai-optics": "^0.4.0", "lodash-es": "^4.17.21", "lucide-react": "^0.522.0", "markmap-lib": "^0.18.12", "markmap-view": "^0.18.12", "mermaid": "^11.9.0", "nanoid": "^5.1.5", "optics-ts": "^2.4.1", "pinyin-pro": "^3.26.0", "react": "^19.1.0", "react-device-detect": "^2.2.3", "react-dom": "^19.1.0", "react-error-boundary": "^4.1.2", "react-fast-marquee": "^1.6.5", "react-i18next": "^14.1.3", "react-markdown": "^10.1.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-sortablejs": "^6.1.4", "react-transition-group": "^4.4.5", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "remark-gfm": "^4.0.1", "siriwave": "^2.4.0", "slate": "^0.103.0", "slate-history": "^0.100.0", "slate-react": "^0.107.1", "sonner": "^2.0.5", "sortablejs": "^1.15.6", "spark-md5": "^3.0.2", "tailwind-merge": "^2.6.0", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@swc/plugin-emotion": "^10.0.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.7", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@types/react-transition-group": "^4.4.12", "@types/sortablejs": "^1.15.8", "@types/spark-md5": "^3.0.5", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12", "tailwindcss": "^3.4.17", "tailwindcss-animated": "^1.1.2", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@10.11.0+sha1.4048eeefd564ff1ab248fac3e2854d38245fe2f1"}