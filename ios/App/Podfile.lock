PODS:
  - Capacitor (7.4.2):
    - Capac<PERSON><PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorBrowser (7.0.1):
    - Capacitor
  - CapacitorCordova (7.4.2)
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapacitorToast (7.0.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.0/node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/browser`)"
  - "CapacitorCordova (from `../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.0/node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/.pnpm/@capacitor+haptics@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/keyboard`)"
  - "CapacitorStatusBar (from `../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../node_modules/.pnpm/@capacitor+toast@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/toast`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.0/node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/browser"
  CapacitorCordova:
    :path: "../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.0/node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/.pnpm/@capacitor+haptics@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/keyboard"
  CapacitorStatusBar:
    :path: "../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../node_modules/.pnpm/@capacitor+toast@7.0.1_@capacitor+core@7.4.0/node_modules/@capacitor/toast"

SPEC CHECKSUMS:
  Capacitor: 9d9e481b79ffaeacaf7a85d6a11adec32bd33b59
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorBrowser: 6299776d496e968505464884d565992faa20444a
  CapacitorCordova: 5e58d04631bc5094894ac106e2bf1da18a9e6151
  CapacitorHaptics: 1f1e17041f435d8ead9ff2a34edd592c6aa6a8d6
  CapacitorKeyboard: 09fd91dcde4f8a37313e7f11bde553ad1ed52036
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  CapacitorToast: ddfc4b36080dbe3634c9faf510b20f6ddcff9330

PODFILE CHECKSUM: 632098d84eb59ecbfbe827f7494efe4d0220bd3a

COCOAPODS: 1.16.2
