# ChatServiceManager 网络状态检测集成指南

## 概述

本文档描述了如何在 ChatServiceManager 中集成 Ionic Network 插件来实现智能的网络状态检测和自动重连功能。

## 功能特性

### 1. 自动网络状态监听
- 实时监听设备网络连接状态变化
- 支持 WiFi、移动数据等各种网络类型
- 在 Web 环境中优雅降级

### 2. 智能重连机制
- 当网络从不可用变为可用时，自动触发重连检查
- 避免在网络不可用时进行无效的连接尝试
- 延迟重连以确保网络稳定

### 3. 详细的日志记录
- 记录网络状态变化事件
- 记录重连尝试和结果
- 便于调试和问题排查

## 安装依赖

```bash
# 安装 Capacitor Network 插件
pnpm add @capacitor/network

# 同步 Capacitor 配置
npx cap sync
```

## 核心实现

### 网络状态属性

```typescript
// 网络状态监听相关
private networkListener: any = null
private isNetworkAvailable: boolean = true
private wasNetworkUnavailable: boolean = false
```

### 初始化网络监听

```typescript
private async initializeNetworkListener(): Promise<void> {
  try {
    // 获取当前网络状态
    const status = await Network.getStatus()
    this.isNetworkAvailable = status.connected
    
    // 监听网络状态变化
    this.networkListener = Network.addListener('networkStatusChange', (status) => {
      this.handleNetworkStatusChange(status)
    })
  } catch (error) {
    // 在 Web 环境中优雅降级
    this.isNetworkAvailable = true
  }
}
```

### 网络状态变化处理

```typescript
private handleNetworkStatusChange(status: NetworkConnectionStatus): void {
  const wasAvailable = this.isNetworkAvailable
  this.isNetworkAvailable = status.connected

  // 如果网络连接可用，检查是否需要重连
  if (status.connected) {
    // 如果之前网络不可用，现在恢复了，触发重连检查
    if (!wasAvailable) {
      console.log('🌐 [ChatServiceManager] 网络恢复，触发重连检查')
      // 延迟一秒后检查重连，给网络一些时间稳定
      setTimeout(() => {
        this.handleNetworkRecovered()
      }, 1000)
    }
  } else {
    // 网络断开
    console.log('🌐 [ChatServiceManager] 网络断开')
  }
}
```

### 智能重连逻辑

```typescript
private async checkHeartbeatAndReconnectIfNeeded(): Promise<boolean> {
  // 只有在网络连接可用时才进行重连检查
  if (this.isNetworkAvailable) {
    // 执行心跳检测和重连逻辑
    const now = Date.now()
    const timeSinceLastHeartbeat = now - this.lastHeartbeatTime

    if (this.lastHeartbeatTime > 0 &&
        timeSinceLastHeartbeat > this.heartbeatTimeoutMs) {
      // 触发重连
      await this.performTransparentReconnect()
      return true
    }
    // 其他重连逻辑...
  } else {
    console.log('💓 [ChatServiceManager] 网络不可用，跳过重连检查')
    return false
  }
}
```

## 使用方法

### 1. 获取网络状态

```typescript
import { chatServiceManager } from '@/tina/services/chat-service-manager'

// 获取当前网络状态
const networkStatus = chatServiceManager.getNetworkStatus()
console.log('网络可用:', networkStatus.isNetworkAvailable)
console.log('曾经断网:', networkStatus.wasNetworkUnavailable)
```

### 2. 监听连接状态变化

```typescript
chatServiceManager.initialize({
  onConnectionStatusChange: (status) => {
    console.log('连接状态变化:', status)
  },
  // 其他回调...
})
```

## 测试页面

项目中包含了一个测试页面 `NetworkTestPage.tsx`，可以用来验证网络状态检测功能：

- 实时显示网络状态和心跳状态
- 提供手动测试重连功能
- 显示详细的日志记录

## 注意事项

### 1. 平台兼容性
- 在移动设备上提供完整的网络状态检测功能
- 在 Web 环境中优雅降级，默认认为网络可用

### 2. 性能考虑
- 网络状态变化时有 1 秒延迟，确保网络稳定后再尝试重连
- 避免频繁的重连尝试

### 3. 错误处理
- 网络插件初始化失败时不影响主要功能
- 重连失败时有详细的错误日志

## 日志示例

```
🌐 [ChatServiceManager] 初始网络状态: 已连接
🌐 [ChatServiceManager] 网络状态监听已初始化
🌐 [ChatServiceManager] 网络状态变化: 已连接 -> 未连接
🌐 [ChatServiceManager] 网络断开
🌐 [ChatServiceManager] 网络状态变化: 未连接 -> 已连接
🌐 [ChatServiceManager] 网络恢复，触发重连检查
🌐 [ChatServiceManager] 网络恢复后重连成功
```

## 总结

通过集成 Ionic Network 插件，ChatServiceManager 现在具备了智能的网络状态感知能力，能够：

1. 实时监听网络状态变化
2. 在网络恢复时自动触发重连
3. 避免在网络不可用时进行无效操作
4. 提供详细的调试信息

这大大提升了应用在网络环境不稳定时的用户体验和连接可靠性。
